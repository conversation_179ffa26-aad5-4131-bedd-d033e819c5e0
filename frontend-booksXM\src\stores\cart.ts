import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { cartAPI } from '@/api'

export interface CartItem {
  id: number
  bookId: number
  book: {
    id: number
    title: string
    author: string
    price: number
    originalPrice?: number
    coverUrl: string
    category: string
    stock: number
  }
  quantity: number
  selected: boolean
}

export const useCartStore = defineStore('cart', () => {
  const items = ref<CartItem[]>([])
  const loading = ref(false)

  // 计算属性
  const totalItems = computed(() =>
    items.value.reduce((sum, item) => sum + item.quantity, 0)
  )

  const selectedItems = computed(() =>
    items.value.filter(item => item.selected)
  )

  const totalPrice = computed(() =>
    selectedItems.value.reduce((sum, item) => sum + (item.book.price * item.quantity), 0)
  )

  // 从本地存储加载购物车
  const loadFromStorage = () => {
    const savedCart = localStorage.getItem('cart')
    if (savedCart) {
      try {
        items.value = JSON.parse(savedCart)
      } catch (error) {
        console.error('Failed to parse cart from storage:', error)
        items.value = []
      }
    }
  }

  // 保存到本地存储
  const saveToStorage = () => {
    localStorage.setItem('cart', JSON.stringify(items.value))
  }

  // 从服务器获取购物车
  const fetchCart = async () => {
    try {
      loading.value = true
      const data = await cartAPI.getCart() as any

      // 转换后端数据格式为前端期望的格式
      const backendItems = data?.items || []

      items.value = backendItems.map((item: any) => ({
        id: item.id,
        bookId: item.book.id,
        book: {
          id: item.book.id,
          title: item.book.title,
          author: item.book.author,
          price: item.book.price,
          originalPrice: item.book.originalPrice,
          coverUrl: item.book.coverUrl || '',
          category: item.book.category,
          stock: item.book.stock
        },
        quantity: item.quantity,
        selected: item.isSelected || item.selected || true // 兼容后端的 isSelected 字段
      }))

      saveToStorage()
    } catch (error) {
      console.error('Failed to fetch cart:', error)
      // 如果服务器请求失败，使用本地存储的数据
      loadFromStorage()
    } finally {
      loading.value = false
    }
  }

  // 添加商品到购物车
  const addItem = async (book: any, quantity: number = 1) => {
    try {
      const existingItem = items.value.find(item => item.bookId === book.id)

      if (existingItem) {
        existingItem.quantity += quantity
      } else {
        const newItem: CartItem = {
          id: Date.now(),
          bookId: book.id,
          book: {
            id: book.id,
            title: book.title,
            author: book.author,
            price: book.price,
            originalPrice: book.originalPrice,
            coverUrl: book.coverUrl || '',
            category: book.category,
            stock: book.stock
          },
          quantity,
          selected: true
        }
        items.value.push(newItem)
      }

      saveToStorage()

      // 同步到服务器
      try {
        await cartAPI.addToCart({ bookId: book.id, quantity })
      } catch (error) {
        console.error('Failed to sync cart to server:', error)
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error)
      throw error
    }
  }

  // 更新商品数量
  const updateQuantity = async (itemId: number, quantity: number) => {
    try {
      const item = items.value.find(item => item.id === itemId)
      if (item) {
        if (quantity <= 0) {
          await removeItem(itemId)
        } else {
          item.quantity = quantity
          saveToStorage()

          // 同步到服务器
          try {
            await cartAPI.updateCartItem(itemId, quantity)
          } catch (error) {
            console.error('Failed to sync cart update to server:', error)
          }
        }
      }
    } catch (error) {
      console.error('Failed to update cart item:', error)
      throw error
    }
  }

  // 移除商品
  const removeItem = async (itemId: number) => {
    try {
      const index = items.value.findIndex(item => item.id === itemId)
      if (index > -1) {
        items.value.splice(index, 1)
        saveToStorage()

        // 同步到服务器
        try {
          await cartAPI.removeFromCart(itemId)
        } catch (error) {
          console.error('Failed to sync cart removal to server:', error)
        }
      }
    } catch (error) {
      console.error('Failed to remove cart item:', error)
      throw error
    }
  }

  // 切换商品选中状态
  const toggleItemSelection = (itemId: number) => {
    const item = items.value.find(item => item.id === itemId)
    if (item) {
      item.selected = !item.selected
      saveToStorage()
    }
  }

  // 全选/取消全选
  const toggleAllSelection = (selected: boolean) => {
    items.value.forEach(item => {
      item.selected = selected
    })
    saveToStorage()
  }

  // 清空购物车
  const clearCart = async () => {
    try {
      items.value = []
      saveToStorage()

      // 同步到服务器
      try {
        await cartAPI.clearCart()
      } catch (error) {
        console.error('Failed to sync cart clear to server:', error)
      }
    } catch (error) {
      console.error('Failed to clear cart:', error)
      throw error
    }
  }

  // 清空选中的商品
  const clearSelectedItems = async () => {
    try {
      const selectedItemIds = selectedItems.value.map(item => item.id)

      // 从本地移除选中的商品
      items.value = items.value.filter(item => !item.selected)
      saveToStorage()

      // 同步到服务器
      for (const itemId of selectedItemIds) {
        try {
          await cartAPI.removeFromCart(itemId)
        } catch (error) {
          console.error(`Failed to remove cart item ${itemId} from server:`, error)
        }
      }
    } catch (error) {
      console.error('Failed to clear selected items:', error)
      throw error
    }
  }

  // 初始化购物车
  const initCart = () => {
    loadFromStorage()
  }

  return {
    items,
    loading,
    totalItems,
    selectedItems,
    totalPrice,
    fetchCart,
    addItem,
    updateQuantity,
    removeItem,
    toggleItemSelection,
    toggleAllSelection,
    clearCart,
    clearSelectedItems,
    initCart
  }
})
