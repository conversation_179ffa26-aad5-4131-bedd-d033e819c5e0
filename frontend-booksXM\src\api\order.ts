import api from './index'

export interface CreateOrderRequest {
  receiverName: string
  receiverPhone: string
  shippingAddress: string
  notes?: string
  paymentMethod: 'ALIPAY' | 'WECHAT' | 'BANK_CARD' 
  items: Array<{
    bookId: number
    quantity: number
    unitPrice: number
  }>
  totalAmount: number
  shippingFee: number
  finalAmount: number
}

export interface Order {
  id: number
  orderNumber: string
  status: 'PENDING' | 'PAID' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
  paymentStatus: 'UNPAID' | 'PAID' | 'REFUNDING' | 'REFUNDED'
  paymentMethod: 'ALIPAY' | 'WECHAT' | 'BANK_CARD' 
  receiverName: string
  receiverPhone: string
  shippingAddress: string
  notes?: string
  totalAmount: number
  shippingFee: number
  discountAmount?: number
  finalAmount: number
  createdAt: string
  updatedAt?: string
  paymentTime?: string
  shippingTime?: string
  deliveryTime?: string
  items: Array<{
    id: number
    bookId: number
    bookTitle: string
    bookAuthor: string
    bookCoverUrl?: string
    bookIsbn?: string
    quantity: number
    unitPrice: number
    subtotal: number
  }>
}

export const orderApi = {
  // 创建订单
  createOrder: (data: CreateOrderRequest): Promise<Order> =>
    api.post('/orders', data),

  // 获取订单列表（用户自己的订单）
  getOrders: (params?: {
    page?: number
    size?: number
    status?: string
  }): Promise<{
    orders: Order[]
    currentPage: number
    totalItems: number
    totalPages: number
    hasNext: boolean
    hasPrevious: boolean
  }> =>
    api.get('/orders/my', { params }),

  // 获取订单详情
  getOrder: (id: number): Promise<Order> =>
    api.get(`/orders/${id}`),

  // 取消订单
  cancelOrder: (id: number): Promise<void> =>
    api.put(`/orders/${id}/cancel`),

  // 确认收货
  confirmOrder: (id: number): Promise<void> =>
    api.put(`/orders/${id}/confirm`),

  // 模拟支付
  simulatePayment: (orderId: number, paymentMethod: string): Promise<void> =>
    api.post(`/orders/${orderId}/pay`, { paymentMethod })
}
