<template>
  <div class="orders-page">
    <div class="container">
      <div class="orders-header">
        <h1>我的订单</h1>
      </div>

      <!-- 订单状态筛选 -->
      <div class="order-filters">
        <el-tabs v-model="activeStatus" @tab-change="handleStatusChange">
          <el-tab-pane label="全部订单" name="" />
          <el-tab-pane label="待付款" name="PENDING" />
          <el-tab-pane label="已付款" name="PAID" />
          <el-tab-pane label="已发货" name="SHIPPED" />
          <el-tab-pane label="已完成" name="DELIVERED" />
          <el-tab-pane label="已取消" name="CANCELLED" />
        </el-tabs>
      </div>

      <!-- 订单列表 -->
      <div class="orders-content" v-loading="loading">
        <div v-if="orders.length === 0 && !loading" class="empty-orders">
          <el-empty description="暂无订单" />
        </div>

        <div v-else class="orders-list">
          <div v-for="order in orders" :key="order.id" class="order-item">
            <div class="order-header">
              <div class="order-info">
                <span class="order-number">订单号：{{ order.orderNumber }}</span>
                <span class="order-date">{{ formatDate(order.createdAt) }}</span>
              </div>
              <div class="order-status">
                <el-tag :type="getStatusType(order.status)">
                  {{ getStatusText(order.status) }}
                </el-tag>
              </div>
            </div>

            <div class="order-content">
              <div class="order-items">
                <div v-for="item in order.orderItems" :key="item.id" class="order-item-detail">
                  <div class="item-image">
                    <img :src="item.book.coverUrl || defaultCover" :alt="item.book.title" />
                  </div>
                  <div class="item-info">
                    <h4 class="item-title">{{ item.book.title }}</h4>
                    <p class="item-author">{{ item.book.author }}</p>
                    <div class="item-price">
                      <span class="price">¥{{ item.price }}</span>
                      <span class="quantity">x{{ item.quantity }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="order-summary">
                <div class="shipping-info">
                  <p><strong>收货地址：</strong>{{ order.shippingAddress }}</p>
                  <p><strong>联系人：</strong>{{ order.contactName }} {{ order.contactPhone }}</p>
                </div>
                <div class="order-total">
                  <span class="total-label">订单总额：</span>
                  <span class="total-amount">¥{{ order.totalAmount }}</span>
                </div>
              </div>
            </div>

            <div class="order-actions">
              <el-button v-if="order.status === 'PENDING'" type="primary" size="small" @click="handlePayOrder(order)">
                立即付款
              </el-button>
              <el-button v-if="order.status === 'PENDING'" size="small" @click="handleCancelOrder(order.id)">
                取消订单
              </el-button>
              <el-button size="small" @click="handleViewOrder(order)">
                查看详情
              </el-button>
              <el-button v-if="order.status === 'SHIPPED'" type="success" size="small"
                @click="handleConfirmOrder(order.id)">
                确认收货
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination" v-if="total > 0">
          <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50]"
            :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
            @current-change="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="orderDetailVisible" title="订单详情" width="800px">
      <div v-if="selectedOrder" class="order-detail">
        <div class="detail-section">
          <h3>订单信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">
              {{ selectedOrder.orderNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusType(selectedOrder.status)">
                {{ getStatusText(selectedOrder.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="下单时间">
              {{ formatDate(selectedOrder.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="订单总额">
              ¥{{ selectedOrder.totalAmount }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="detail-section">
          <h3>商品信息</h3>
          <el-table :data="selectedOrder.orderItems" style="width: 100%">
            <el-table-column label="商品" width="300">
              <template #default="{ row }">
                <div class="product-info">
                  <img :src="row.book.coverUrl || defaultCover" :alt="row.book.title" class="product-image" />
                  <div>
                    <div class="product-title">{{ row.book.title }}</div>
                    <div class="product-author">{{ row.book.author }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="price" label="单价" width="100">
              <template #default="{ row }">
                ¥{{ row.price }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="80" />
            <el-table-column label="小计">
              <template #default="{ row }">
                ¥{{ (row.price * row.quantity).toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="detail-section">
          <h3>收货信息</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="收货地址">
              {{ selectedOrder.shippingAddress }}
            </el-descriptions-item>
            <el-descriptions-item label="联系人">
              {{ selectedOrder.contactName }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ selectedOrder.contactPhone }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { orderAPI } from '@/api'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { Order } from '@/types'

  // 路由
  const route = useRoute()

  // 响应式数据
  const orders = ref<Order[]>([])
  const loading = ref(false)
  const activeStatus = ref('')
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)
  const orderDetailVisible = ref(false)
  const selectedOrder = ref<Order | null>(null)
  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=100&h=120&fit=crop')

  // 获取订单列表
  const fetchOrders = async () => {
    try {
      loading.value = true

      const params: any = {
        page: currentPage.value - 1,
        size: pageSize.value
      }

      if (activeStatus.value) {
        params.status = activeStatus.value
      }

      const response = await orderAPI.getOrders(params)
      orders.value = (response as any)?.orders || response || []
      total.value = (response as any)?.total || orders.value.length

    } catch (error) {
      console.error('获取订单列表失败:', error)
      ElMessage.error('获取订单列表失败')
    } finally {
      loading.value = false
    }
  }

  // 状态筛选
  const handleStatusChange = (status: string) => {
    activeStatus.value = status
    currentPage.value = 1
    fetchOrders()
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    currentPage.value = page
    fetchOrders()
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchOrders()
  }

  // 订单操作
  const handlePayOrder = (order: Order) => {
    ElMessage.info('跳转到支付页面...')
    // 这里可以集成真实的支付系统
  }

  const handleCancelOrder = async (orderId: number) => {
    try {
      await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await orderAPI.cancelOrder(orderId)
      ElMessage.success('订单已取消')
      await fetchOrders()

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('取消订单失败')
      }
    }
  }

  const handleConfirmOrder = async (orderId: number) => {
    try {
      await ElMessageBox.confirm('确认已收到商品？', '提示', {
        confirmButtonText: '确认收货',
        cancelButtonText: '取消',
        type: 'info'
      })

      await orderAPI.confirmOrder(orderId)
      ElMessage.success('确认收货成功')
      await fetchOrders()

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('确认收货失败')
      }
    }
  }

  const handleViewOrder = (order: Order) => {
    selectedOrder.value = order
    orderDetailVisible.value = true
  }

  // 工具函数
  const getStatusType = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: 'warning',
      PAID: 'info',
      SHIPPED: 'primary',
      DELIVERED: 'success',
      CANCELLED: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: '待付款',
      PAID: '已付款',
      SHIPPED: '已发货',
      DELIVERED: '已完成',
      CANCELLED: '已取消'
    }
    return statusMap[status] || status
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 页面初始化
  onMounted(() => {
    // 从路由查询参数中读取初始状态
    const statusFromQuery = route.query.status as string
    if (statusFromQuery) {
      activeStatus.value = statusFromQuery
    }
    fetchOrders()
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  })
</script>

<style scoped>
  .orders-page {
    min-height: 100vh;
    background: #f5f5f5;
    padding: 20px 0;
  }

  .orders-header {
    margin-bottom: 20px;
  }

  .orders-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .order-filters {
    background: white;
    border-radius: 8px;
    padding: 0 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .orders-content {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .empty-orders {
    padding: 60px 20px;
    text-align: center;
  }

  .orders-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
  }

  .order-item {
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    overflow: hidden;
  }

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .order-info {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .order-number {
    font-weight: 600;
    color: #303133;
  }

  .order-date {
    color: #909399;
    font-size: 14px;
  }

  .order-content {
    padding: 20px;
  }

  .order-items {
    margin-bottom: 20px;
  }

  .order-item-detail {
    display: flex;
    gap: 16px;
    padding: 12px 0;
    border-bottom: 1px solid #f5f5f5;
  }

  .order-item-detail:last-child {
    border-bottom: none;
  }

  .item-image {
    width: 60px;
    height: 80px;
    flex-shrink: 0;
  }

  .item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }

  .item-info {
    flex: 1;
  }

  .item-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 8px 0;
    line-height: 1.4;
  }

  .item-author {
    color: #909399;
    font-size: 14px;
    margin: 0 0 8px 0;
  }

  .item-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .price {
    font-size: 16px;
    font-weight: 600;
    color: #e6a23c;
  }

  .quantity {
    color: #606266;
  }

  .order-summary {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }

  .shipping-info p {
    margin: 0 0 4px 0;
    color: #606266;
    font-size: 14px;
  }

  .order-total {
    text-align: right;
  }

  .total-label {
    color: #606266;
    margin-right: 8px;
  }

  .total-amount {
    font-size: 18px;
    font-weight: 600;
    color: #e6a23c;
  }

  .order-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    background: #fafafa;
    border-top: 1px solid #f0f0f0;
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }

  .order-detail {
    max-height: 600px;
    overflow-y: auto;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .product-info {
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .product-image {
    width: 40px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
  }

  .product-title {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .product-author {
    font-size: 12px;
    color: #909399;
  }

  @media (max-width: 768px) {
    .orders-content {
      padding: 16px;
    }

    .order-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .order-info {
      flex-direction: column;
      gap: 8px;
    }

    .order-summary {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .order-actions {
      flex-wrap: wrap;
      justify-content: center;
    }

    .item-price {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }
  }
</style>
