package com.booksxm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

@Service
public class FileStorageService {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageService.class);
    
    private final Path fileStorageLocation;
    
    // 文件存储路径，默认为项目根目录下的uploads文件夹
    public FileStorageService(@Value("${file.upload-dir:uploads}") String uploadDir) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            logger.error("无法创建文件上传目录: {}", ex.getMessage());
            throw new RuntimeException("无法创建文件上传目录", ex);
        }
    }
    
    /**
     * 存储文件到系统
     * 
     * @param file 上传的文件
     * @return 存储的文件名
     */
    public String storeFile(MultipartFile file) {
        // 规范化文件名
        String originalFileName = StringUtils.cleanPath(file.getOriginalFilename());
        
        try {
            // 检查文件名是否包含非法字符
            if (originalFileName.contains("..")) {
                throw new RuntimeException("文件名包含非法路径序列 " + originalFileName);
            }
            
            // 生成唯一文件名，避免文件覆盖
            String fileExtension = "";
            if (originalFileName.contains(".")) {
                fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            }
            String fileName = UUID.randomUUID().toString() + fileExtension;
            
            // 复制文件到目标位置
            Path targetLocation = this.fileStorageLocation.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            
            logger.info("文件上传成功: {}", fileName);
            return fileName;
        } catch (IOException ex) {
            logger.error("文件存储失败: {}", ex.getMessage());
            throw new RuntimeException("文件存储失败", ex);
        }
    }
    
    /**
     * 加载文件作为Resource
     * 
     * @param fileName 文件名
     * @return 文件资源
     */
    public Resource loadFileAsResource(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            if (resource.exists()) {
                return resource;
            } else {
                logger.error("文件不存在: {}", fileName);
                throw new RuntimeException("文件不存在 " + fileName);
            }
        } catch (MalformedURLException ex) {
            logger.error("文件URL格式错误: {}", ex.getMessage());
            throw new RuntimeException("文件URL格式错误 " + fileName, ex);
        }
    }
    
    /**
     * 获取文件的完整访问路径
     * 
     * @param fileName 文件名
     * @return 文件的访问URL
     */
    public String getFileUrl(String fileName) {
        return "/api/uploads/" + fileName;
    }
}
