-- 插入测试用户数据（明文密码）
INSERT INTO users (username, email, password, role, is_active, email_verified, created_at, updated_at) VALUES
('admin', '<EMAIL>', 'admin123', 'ADMIN', true, true, NOW(), NOW()),
('user', '<EMAIL>', 'user123', 'USER', true, true, NOW(), NOW()),

-- 插入测试图书数据
INSERT INTO books (title, author, isbn, publisher, publish_date, price, original_price, stock, description, category, page_count, language, sales_count, view_count, rating, rating_count, is_active, is_featured, created_at, updated_at) VALUES
('Java核心技术 卷I', '<PERSON><PERSON> <PERSON><PERSON>', '9787111213826', '机械工业出版社', '2020-01-01', 89.00, 99.00, 50, 'Java编程经典教材，全面介绍Java语言的核心概念和高级特性。', '计算机', 800, '中文', 120, 1500, 4.8, 95, true, true, NOW(), NOW()),
('Spring Boot实战', '克雷格·沃斯', '9787115123456', '人民邮电出版社', '2021-03-15', 79.00, 89.00, 30, 'Spring Boot开发实战指南，从入门到精通。', '计算机', 450, '中文', 85, 980, 4.6, 67, true, true, NOW(), NOW()),
('算法导论', '<PERSON> H. Cormen', '9787111407010', '机械工业出版社', '2019-09-01', 128.00, 148.00, 25, '计算机科学领域的经典教材，全面介绍算法设计与分析。', '计算机', 1200, '中文', 200, 2300, 4.9, 156, true, true, NOW(), NOW()),
('深入理解计算机系统', 'Randal E. Bryant', '9787111321312', '机械工业出版社', '2020-05-20', 139.00, 159.00, 40, '从程序员的角度深入理解计算机系统的工作原理。', '计算机', 736, '中文', 150, 1800, 4.7, 89, true, false, NOW(), NOW()),
('设计模式', 'Erich Gamma', '9787111075776', '机械工业出版社', '2018-12-01', 69.00, 79.00, 60, '面向对象设计的经典之作，介绍23种设计模式。', '计算机', 395, '中文', 300, 2500, 4.8, 178, true, true, NOW(), NOW()),
('红楼梦', '曹雪芹', '9787020002207', '人民文学出版社', '2019-01-01', 45.00, 55.00, 100, '中国古典文学四大名著之一，描绘了贾宝玉、林黛玉等人的爱情悲剧。', '文学', 1200, '中文', 500, 3200, 4.9, 245, true, true, NOW(), NOW()),
('西游记', '吴承恩', '9787020002214', '人民文学出版社', '2019-02-01', 42.00, 52.00, 80, '中国古典文学四大名著之一，讲述孙悟空等人西天取经的故事。', '文学', 1000, '中文', 450, 2800, 4.8, 198, true, false, NOW(), NOW()),
('三国演义', '罗贯中', '9787020002221', '人民文学出版社', '2019-03-01', 48.00, 58.00, 70, '中国古典文学四大名著之一，描述了三国时期的历史故事。', '文学', 1100, '中文', 380, 2400, 4.7, 167, true, false, NOW(), NOW()),
('水浒传', '施耐庵', '9787020002238', '人民文学出版社', '2019-04-01', 46.00, 56.00, 90, '中国古典文学四大名著之一，讲述了108位好汉的故事。', '文学', 1050, '中文', 420, 2600, 4.8, 189, true, false, NOW(), NOW()),
('平凡的世界', '路遥', '9787020002245', '人民文学出版社', '2020-01-01', 68.00, 78.00, 120, '茅盾文学奖获奖作品，描绘了改革开放初期农村的变迁。', '文学', 1680, '中文', 600, 4500, 4.9, 312, true, true, NOW(), NOW()),
('活着', '余华', '9787020002252', '人民文学出版社', '2020-02-01', 35.00, 45.00, 150, '当代文学经典，讲述了一个人的一生和命运的变迁。', '文学', 256, '中文', 800, 5200, 4.8, 456, true, true, NOW(), NOW()),
('百年孤独', '加西亚·马尔克斯', '9787020002269', '人民文学出版社', '2020-03-01', 55.00, 65.00, 85, '魔幻现实主义文学的代表作，诺贝尔文学奖获奖作品。', '文学', 360, '中文', 280, 1900, 4.7, 134, true, false, NOW(), NOW()),
('人类简史', '尤瓦尔·赫拉利', '9787508660752', '中信出版社', '2021-01-01', 68.00, 78.00, 200, '从认知革命到科学革命，人类发展的宏大叙事。', '历史', 512, '中文', 350, 2800, 4.6, 198, true, true, NOW(), NOW()),
('未来简史', '尤瓦尔·赫拉利', '9787508672069', '中信出版社', '2021-02-01', 72.00, 82.00, 180, '人工智能时代，人类的未来走向何方？', '历史', 480, '中文', 280, 2200, 4.5, 156, true, false, NOW(), NOW()),
('今日简史', '尤瓦尔·赫拉利', '9787508692067', '中信出版社', '2021-03-01', 65.00, 75.00, 160, '21世纪的21堂课，关于当下世界的思考。', '历史', 400, '中文', 220, 1800, 4.4, 123, true, false, NOW(), NOW());

-- 注意：现在使用明文密码，admin密码是admin123，其他用户密码是password
