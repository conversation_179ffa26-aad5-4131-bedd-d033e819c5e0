<template>
  <div class="user-management">
    <div class="page-header">
      <h1>用户管理</h1>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="输入用户名" clearable />
        </el-form-item>
        <el-form-item label="邮箱">
          <el-input v-model="searchForm.email" placeholder="输入邮箱" clearable />
        </el-form-item>
        <el-form-item label="角色" class="Role">
          <el-select v-model="searchForm.role" placeholder="选择角色" clearable>
            <el-option label="全部" value="" />
            <el-option label="普通用户" value="USER" />
            <el-option label="管理员" value="ADMIN" />
          </el-select>
        </el-form-item>
        <el-form-item label="注册时间">
          <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="table-card">
      <el-table :data="users" v-loading="loading" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />

        <el-table-column prop="id" value="ID" width="80" />

        <el-table-column prop="username" value="用户名" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <div class="username">{{ row.username }}</div>
              <div class="user-id">#{{ row.id }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="email" value="邮箱" width="200" />

        <el-table-column value="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.role === 'ADMIN' ? 'danger' : 'primary'">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column value="订单统计" width="120">
          <template #default="{ row }">
            <div class="order-stats">
              <div class="order-count">{{ row.orderCount || 0 }}单</div>
              <div class="order-amount">¥{{ row.totalAmount || 0 }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column value="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'">
              {{ row.isActive ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column value="最后登录" width="180">
          <template #default="{ row }">
            {{ row.lastLoginAt ? formatDate(row.lastLoginAt) : '从未登录' }}
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" value="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column value="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewUser(row)">
              查看
            </el-button>
            <el-button size="small" :type="row.isActive ? 'danger' : 'success'" @click="handleToggleUserStatus(row)">
              {{ row.isActive ? '禁用' : '启用' }}
            </el-button>
            <el-dropdown @command="(command: string) => handleUserAction(command, row)">
              <el-button size="small">
                更多<el-icon>
                  <ArrowDown />
                </el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="reset-password">重置密码</el-dropdown-item>
                  <el-dropdown-item command="view-orders">查看订单</el-dropdown-item>
                  <el-dropdown-item command="change-role" divided>
                    {{ row.role === 'ADMIN' ? '设为用户' : '设为管理员' }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedUsers.length > 0">
        <span>已选择 {{ selectedUsers.length }} 项</span>
        <el-button size="small" @click="handleBatchEnable">批量启用</el-button>
        <el-button size="small" @click="handleBatchDisable">批量禁用</el-button>
        <el-button size="small" @click="handleExportUsers">导出用户</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="userDetailVisible" title="用户详情" width="800px">
      <div v-if="selectedUser" class="user-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">
              {{ selectedUser.id }}
            </el-descriptions-item>
            <el-descriptions-item label="用户名">
              {{ selectedUser.username }}
            </el-descriptions-item>
            <el-descriptions-item label="邮箱">
              {{ selectedUser.email }}
            </el-descriptions-item>
            <el-descriptions-item label="角色">
              <el-tag :type="selectedUser.role === 'ADMIN' ? 'danger' : 'primary'">
                {{ getRoleText(selectedUser.role) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedUser.isActive ? 'success' : 'danger'">
                {{ selectedUser.isActive ? '正常' : '禁用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="注册时间">
              {{ formatDate(selectedUser.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="最后登录">
              {{ selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : '从未登录' }}
            </el-descriptions-item>
            <el-descriptions-item label="最后更新">
              {{ formatDate(selectedUser.updatedAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 统计信息 -->
        <div class="detail-section">
          <h3>统计信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="订单总数" :value="selectedUser.orderCount || 0" suffix="单" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="消费总额" :value="selectedUser.totalAmount || 0" prefix="¥" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="平均订单" :value="selectedUser.avgOrderAmount || 0" prefix="¥" />
            </el-col>
          </el-row>
        </div>

        <!-- 地址信息 -->
        <div class="detail-section" v-if="selectedUser.addresses && selectedUser.addresses.length > 0">
          <h3>收货地址</h3>
          <div class="addresses-list">
            <div v-for="address in selectedUser.addresses" :key="address.id" class="address-item">
              <div class="address-header">
                <span class="name">{{ address.name }}</span>
                <span class="phone">{{ address.phone }}</span>
                <el-tag v-if="address.isDefault" type="success" size="small">默认</el-tag>
              </div>
              <div class="address-detail">
                {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="detail-actions">
          <el-button :type="selectedUser.isActive ? 'danger' : 'success'" @click="handleToggleUserStatus(selectedUser)">
            {{ selectedUser.isActive ? '禁用用户' : '启用用户' }}
          </el-button>
          <el-button @click="handleResetPassword(selectedUser)">
            重置密码
          </el-button>
          <el-button @click="handleViewUserOrders(selectedUser)">
            查看订单
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { adminAPI } from '@/api'
  import type { User } from '@/types'

  // 响应式数据
  const users = ref<User[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const selectedUsers = ref<User[]>([])
  const userDetailVisible = ref(false)
  const selectedUser = ref<User | null>(null)

  // 搜索表单
  const searchForm = ref({
    username: '',
    email: '',
    role: '',
    dateRange: null as any
  })


  // 获取用户列表
  const fetchUsers = async () => {
    try {
      loading.value = true

      const params: any = {
        page: currentPage.value - 1,
        size: pageSize.value
      }

      if (searchForm.value.username) {
        params.username = searchForm.value.username
      }

      if (searchForm.value.email) {
        params.email = searchForm.value.email
      }

      if (searchForm.value.role) {
        params.role = searchForm.value.role
      }

      if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
        params.startDate = searchForm.value.dateRange[0] + 'T00:00:00'
        params.endDate = searchForm.value.dateRange[1] + 'T23:59:59'
      }

      const response = await adminAPI.getUsers(params)
      users.value = (response as any)?.users || response || []
      total.value = (response as any)?.totalItems || users.value.length

    } catch (error) {
      console.error('获取用户列表失败:', error)
      ElMessage.error('获取用户列表失败')
    } finally {
      loading.value = false
    }
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    fetchUsers()
  }

  const handleReset = () => {
    searchForm.value = {
      username: '',
      email: '',
      role: '',
      dateRange: null
    }
    handleSearch()
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    currentPage.value = page
    fetchUsers()
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchUsers()
  }

  // 选择处理
  const handleSelectionChange = (selection: User[]) => {
    selectedUsers.value = selection
  }

  // 用户操作
  const handleViewUser = (user: User) => {
    selectedUser.value = user
    userDetailVisible.value = true
  }

  const handleToggleUserStatus = async (user: User) => {
    try {
      const action = user.isActive ? '禁用' : '启用'
      await ElMessageBox.confirm(`确定要${action}用户 ${user.username} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await adminAPI.updateUserStatus(user.id, { isActive: !user.isActive })
      user.isActive = !user.isActive
      ElMessage.success(`用户${action}成功`)

      if (userDetailVisible.value) {
        userDetailVisible.value = false
      }

    } catch (error) {
      if (error !== 'cancel') {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    }
  }

  const handleUserAction = async (command: string, user: User) => {
    switch (command) {
      case 'reset-password':
        await handleResetPassword(user)
        break
      case 'view-orders':
        handleViewUserOrders(user)
        break
      case 'change-role':
        await handleChangeUserRole(user)
        break
    }
  }

  const handleResetPassword = async (user: User) => {
    try {
      await ElMessageBox.confirm(`确定要重置用户 ${user.username} 的密码吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const response = await adminAPI.resetUserPassword(user.id)
      const newPassword = (response as any).newPassword

      await ElMessageBox.alert(
        `用户 ${user.username} 的新密码是：${newPassword}\n请告知用户并提醒其及时修改密码。`,
        '密码重置成功',
        {
          confirmButtonText: '确定',
          type: 'success'
        }
      )

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('重置密码失败')
      }
    }
  }

  const handleViewUserOrders = (user: User) => {
    ElMessage.info(`查看用户 ${user.username} 的订单`)
    // 这里可以跳转到订单管理页面并筛选该用户的订单
  }

  const handleChangeUserRole = async (user: User) => {
    try {
      const newRole = user.role === 'ADMIN' ? 'USER' : 'ADMIN'
      const roleText = newRole === 'ADMIN' ? '管理员' : '普通用户'

      await ElMessageBox.confirm(`确定要将用户 ${user.username} 设为${roleText}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await adminAPI.updateUserRole(user.id, { role: newRole })
      user.role = newRole
      ElMessage.success(`用户角色修改成功`)

    } catch (error) {
      if (error !== 'cancel') {
        console.error('修改角色失败:', error)
        ElMessage.error('修改角色失败')
      }
    }
  }

  // 批量操作
  const handleBatchEnable = async () => {
    try {
      await ElMessageBox.confirm(`确定要批量启用 ${selectedUsers.value.length} 个用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      })

      ElMessage.info('批量启用功能开发中...')

    } catch (error) {
      // 用户取消操作
    }
  }

  const handleBatchDisable = async () => {
    try {
      await ElMessageBox.confirm(`确定要批量禁用 ${selectedUsers.value.length} 个用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      ElMessage.info('批量禁用功能开发中...')

    } catch (error) {
      // 用户取消操作
    }
  }

  const handleExportUsers = () => {
    ElMessage.info('导出用户功能开发中...')
  }

  // 工具函数
  const getRoleText = (role: string) => {
    return role === 'ADMIN' ? '管理员' : '普通用户'
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 页面初始化
  onMounted(() => {
    fetchUsers()
    // 滚动到顶部
    window.scrollTo({ top: 0 })
  })


</script>

<style scoped>
  .user-management {
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    margin-bottom: 20px;
  }

  .page-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .search-card {
    margin-bottom: 20px;
  }
  .Role{
    width: 120px;
  }
  .table-card {
    margin-bottom: 20px;
  }

  .user-info {
    font-size: 14px;
  }

  .username {
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }

  .user-id {
    color: #909399;
    font-size: 12px;
  }

  .order-stats {
    font-size: 14px;
    text-align: center;
  }

  .order-count {
    color: #606266;
    margin-bottom: 2px;
  }

  .order-amount {
    color: #e6a23c;
    font-weight: 500;
  }

  .batch-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }

  .user-detail {
    max-height: 600px;
    overflow-y: auto;
  }

  .detail-section {
    margin-bottom: 24px;
  }

  .detail-section h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .addresses-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .address-item {
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fafafa;
  }

  .address-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;
  }

  .address-header .name {
    font-weight: 500;
    color: #303133;
  }

  .address-header .phone {
    color: #606266;
  }

  .address-detail {
    color: #606266;
    font-size: 14px;
  }

  .detail-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }

  @media (max-width: 768px) {
    .batch-actions {
      flex-wrap: wrap;
      gap: 8px;
    }

    .detail-actions {
      flex-wrap: wrap;
      justify-content: center;
    }
  }
</style>
