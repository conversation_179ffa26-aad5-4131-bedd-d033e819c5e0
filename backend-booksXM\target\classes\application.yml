server:
  port: 9090
  servlet:
    context-path: /api

spring:
  application:
    name: booksxm-backend
  
  # Database Configuration
  datasource:
    url: ***************************************************************************************
    driver-class-name: com.mysql.jdbc.Driver
    username: root
    password: 123456

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    database-platform: org.hibernate.dialect.MySQL57Dialect
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.MySQLDialect
    defer-datasource-initialization: true

  # SQL initialization
  sql:
    init:
      mode: never
      # data-locations: classpath:data.sql
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# File Storage Configuration
file:
  upload-dir: uploads
  
  # Mail Configuration (for development)
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:booksxm-secret-key-for-jwt-token-generation-2024}
  expiration: 86400000 # 24 hours in milliseconds

# CORS Configuration
cors:
  allowed-origins: http://localhost:5173,http://localhost:3000
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Logging Configuration
logging:
  level:
    com.booksxm: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
