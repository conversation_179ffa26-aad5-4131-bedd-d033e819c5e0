package com.booksxm.service;

import com.booksxm.dto.UserInfoDTO;
import com.booksxm.entity.Address;
import com.booksxm.entity.User;
import com.booksxm.repository.AddressRepository;
import com.booksxm.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map;

@Service
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AddressRepository addressRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 根据用户名查找用户
     */
    public User findByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("用户不存在"));
    }

    /**
     * 获取用户信息
     */
    public UserInfoDTO getUserInfo(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setEmail(user.getEmail());
        userInfo.setRealName(user.getRealName());
        userInfo.setPhoneNumber(user.getPhoneNumber());
        userInfo.setAvatarUrl(user.getAvatarUrl());
        userInfo.setAddress(user.getAddress());
        userInfo.setCreatedAt(user.getCreatedAt());
        userInfo.setUpdatedAt(user.getUpdatedAt());

        return userInfo;
    }

    /**
     * 更新用户信息
     */
    @Transactional
    public UserInfoDTO updateUserInfo(Long userId, Map<String, Object> userInfo) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (userInfo.containsKey("realName")) {
            user.setRealName((String) userInfo.get("realName"));
        }
        if (userInfo.containsKey("phoneNumber")) {
            user.setPhoneNumber((String) userInfo.get("phoneNumber"));
        }
        if (userInfo.containsKey("avatarUrl")) {
            user.setAvatarUrl((String) userInfo.get("avatarUrl"));
        }
        if (userInfo.containsKey("address")) {
            user.setAddress((String) userInfo.get("address"));
        }

        user.setUpdatedAt(LocalDateTime.now());
        User updatedUser = userRepository.save(user);

        return getUserInfo(updatedUser.getId());
    }

    /**
     * 修改密码
     */
    @Transactional
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码不正确");
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);
    }

    /**
     * 获取用户地址列表
     */
    public List<Address> getUserAddresses(Long userId) {
        return addressRepository.findByUserIdOrderByIsDefaultDescCreatedAtDesc(userId);
    }

    /**
     * 添加地址
     */
    @Transactional
    public Address addAddress(Long userId, Address address) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        address.setUser(user);

        // 如果是默认地址，清除其他默认地址标记
        if (Boolean.TRUE.equals(address.getIsDefault())) {
            addressRepository.clearDefaultByUserId(userId);
        }
        // 如果是第一个地址，自动设为默认地址
        else if (addressRepository.countByUserId(userId) == 0) {
            address.setIsDefault(true);
        }

        return addressRepository.save(address);
    }

    /**
     * 更新地址
     */
    @Transactional
    public Address updateAddress(Long userId, Long addressId, Address addressDetails) {
        Address address = addressRepository.findByIdAndUserId(addressId, userId)
                .orElseThrow(() -> new RuntimeException("地址不存在或无权限"));

        address.setName(addressDetails.getName());
        address.setPhone(addressDetails.getPhone());
        address.setProvince(addressDetails.getProvince());
        address.setCity(addressDetails.getCity());
        address.setDistrict(addressDetails.getDistrict());
        address.setDetail(addressDetails.getDetail());
        address.setPostalCode(addressDetails.getPostalCode());

        // 如果设为默认地址，清除其他默认地址标记
        if (Boolean.TRUE.equals(addressDetails.getIsDefault()) && !Boolean.TRUE.equals(address.getIsDefault())) {
            addressRepository.clearDefaultByUserId(userId);
            address.setIsDefault(true);
        }

        address.setUpdatedAt(LocalDateTime.now());
        return addressRepository.save(address);
    }

    /**
     * 删除地址
     */
    @Transactional
    public void deleteAddress(Long userId, Long addressId) {
        Address address = addressRepository.findByIdAndUserId(addressId, userId)
                .orElseThrow(() -> new RuntimeException("地址不存在或无权限"));

        addressRepository.delete(address);

        // 如果删除的是默认地址，将第一个地址设为默认地址
        if (Boolean.TRUE.equals(address.getIsDefault())) {
            List<Address> addresses = addressRepository.findByUserIdOrderByIsDefaultDescCreatedAtDesc(userId);
            if (!addresses.isEmpty()) {
                Address firstAddress = addresses.get(0);
                firstAddress.setIsDefault(true);
                addressRepository.save(firstAddress);
            }
        }
    }

    /**
     * 管理员功能：获取用户列表（带过滤条件）
     */
    public Page<User> getUsersWithFilters(String username, String email, User.Role role,
            LocalDateTime startDate, LocalDateTime endDate, Pageable pageable) {
        Specification<User> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (username != null && !username.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("username")),
                        "%" + username.toLowerCase() + "%"));
            }

            if (email != null && !email.trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("email")),
                        "%" + email.toLowerCase() + "%"));
            }

            if (role != null) {
                predicates.add(criteriaBuilder.equal(root.get("role"), role));
            }

            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), startDate));
            }

            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), endDate));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return userRepository.findAll(spec, pageable);
    }

    /**
     * 管理员功能：更新用户状态
     */
    @Transactional
    public User updateUserStatus(Long userId, Boolean isActive) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setIsActive(isActive);
        user.setUpdatedAt(LocalDateTime.now());
        return userRepository.save(user);
    }

    /**
     * 管理员功能：更新用户角色
     */
    @Transactional
    public User updateUserRole(Long userId, User.Role role) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setRole(role);
        user.setUpdatedAt(LocalDateTime.now());
        return userRepository.save(user);
    }

    /**
     * 管理员功能：重置用户密码
     */
    @Transactional
    public String resetUserPassword(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 生成新密码（6位随机数字）
        String newPassword = String.format("%06d", new Random().nextInt(1000000));
        user.setPassword(passwordEncoder.encode(newPassword));
        user.setUpdatedAt(LocalDateTime.now());
        userRepository.save(user);

        logger.info("用户 {} 的密码已重置", user.getUsername());
        return newPassword;
    }

    /**
     * 管理员功能：获取仪表盘统计数据
     */
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();

        // 用户统计
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countByIsActive(true);
        long adminUsers = userRepository.countByRole(User.Role.ADMIN);

        stats.put("totalUsers", totalUsers);
        stats.put("activeUsers", activeUsers);
        stats.put("adminUsers", adminUsers);
        stats.put("inactiveUsers", totalUsers - activeUsers);

        // 最近注册用户
        LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1);
        long newUsersThisWeek = userRepository.countByCreatedAtAfter(oneWeekAgo);
        stats.put("newUsersThisWeek", newUsersThisWeek);

        return stats;
    }
}
