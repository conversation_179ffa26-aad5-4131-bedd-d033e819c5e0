<template>
  <div class="book-card" @click="goToDetail">
    <div class="book-cover">
      <img :src="book.coverUrl || defaultCover" :alt="book.title" @error="handleImageError" />
      <div class="book-overlay">
        <el-button type="primary" size="small" @click.stop="handleAddToCart" :disabled="book.stock <= 0">
          <el-icon>
            <ShoppingCart />
          </el-icon>
          加入购物车
        </el-button>
      </div>

      <!-- 标签 -->
      <div class="book-badges">
        <el-tag v-if="book.isFeatured" type="danger" size="small">推荐</el-tag>
        <el-tag v-if="book.stock <= 0" type="info" size="small">缺货</el-tag>
        <el-tag v-if="book.originalPrice && book.price < book.originalPrice" type="warning" size="small">
          特价
        </el-tag>
      </div>
    </div>

    <div class="book-info">
      <h3 class="book-title" :title="book.title">{{ book.title }}</h3>
      <p class="book-author">{{ book.author }}</p>

      <div class="book-rating" v-if="book.rating > 0">
        <el-rate v-model="book.rating" disabled show-score text-color="#ff9900" score-template="{value}" size="small" />
        <span class="rating-count">({{ book.ratingCount }})</span>
      </div>

      <div class="book-price">
        <span class="current-price">¥{{ book.price }}</span>
        <span v-if="book.originalPrice && book.price < book.originalPrice" class="original-price">
          ¥{{ book.originalPrice }}
        </span>
      </div>

      <div class="book-meta">
        <span class="book-category">{{ book.category }}</span>
        <span class="book-sales">销量: {{ book.salesCount || 0 }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'
  import type { Book } from '@/types'

  interface Props {
    book: Book
  }

  interface Emits {
    (e: 'add-to-cart', book: Book): void
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()
  const router = useRouter()

  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=300&h=400&fit=crop')

  // 处理图片加载错误
  const handleImageError = (event: Event) => {
    const img = event.target as HTMLImageElement
    img.src = defaultCover.value
  }

  // 跳转到详情页
  const goToDetail = () => {
    router.push(`/book/${props.book.id}`)
  }

  // 添加到购物车
  const handleAddToCart = () => {
    emit('add-to-cart', props.book)
  }
</script>

<style scoped>
  .book-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .book-card:hover {
    /* transform: translateY(-4px); */
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    /*蓝色发光效果 */
    box-shadow: 0 0 10px 3px rgba(248, 10, 30, 0.5); 
  }

  .book-cover {
    position: relative;
    width: 100%;
    /* height: 240px; */
    overflow: hidden;
  }

  .book-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .book-card:hover .book-cover img {
    transform: scale(1.05);
  }

  .book-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .book-card:hover .book-overlay {
    opacity: 1;
    transform: scale(1.5);
  }

  .book-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .book-info {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .book-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .book-author {
    font-size: 14px;
    color: #909399;
    margin: 0 0 12px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .book-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }

  .rating-count {
    font-size: 12px;
    color: #909399;
  }

  .book-price {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }

  .current-price {
    font-size: 18px;
    font-weight: 600;
    color: #FF2832;
  }

  .original-price {
    font-size: 14px;
    color: #909399;
    text-decoration: line-through;
  }

  .book-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    font-size: 12px;
    color: #909399;
  }

  .book-category {
    background: #f0f9ff;
    color: #409EFF;
    padding: 2px 8px;
    border-radius: 12px;
  }

  .book-sales {
    font-size: 12px;
  }

  @media (max-width: 768px) {
    .book-cover {
      height: 200px;
    }

    .book-info {
      padding: 12px;
    }

    .book-title {
      font-size: 14px;
    }
  }
</style>
