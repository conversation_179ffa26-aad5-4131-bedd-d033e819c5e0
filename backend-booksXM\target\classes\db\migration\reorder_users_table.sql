-- 重新排列users表的列顺序
-- 注意：MySQL不支持直接重排列顺序，需要重建表

-- 1. 创建临时表保存数据
CREATE TABLE users_temp AS SELECT * FROM users;

-- 2. 删除原表
DROP TABLE users;

-- 3. 按照新的列顺序重新创建表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    role ENUM('USER', 'ADMIN') NOT NULL DEFAULT 'USER',
    email_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    address TEXT,
    avatar_url VARCHAR(500),
    real_name VARCHAR(100)
);

-- 4. 将数据按新顺序插入
INSERT INTO users (
    id, username, email, password, phone_number, role, 
    email_verified, is_active, created_at, updated_at, 
    address, avatar_url, real_name
)
SELECT 
    id, username, email, password, phone_number, role,
    email_verified, is_active, created_at, updated_at,
    address, avatar_url, real_name
FROM users_temp;

-- 5. 删除临时表
DROP TABLE users_temp;

-- 6. 重新创建相关的外键约束（如果有的话）
-- 这里需要根据实际的外键关系来添加
-- ALTER TABLE orders ADD CONSTRAINT fk_orders_user_id FOREIGN KEY (user_id) REFERENCES users(id);
-- ALTER TABLE cart_items ADD CONSTRAINT fk_cart_items_user_id FOREIGN KEY (user_id) REFERENCES users(id);
-- ALTER TABLE addresses ADD CONSTRAINT fk_addresses_user_id FOREIGN KEY (user_id) REFERENCES users(id);
