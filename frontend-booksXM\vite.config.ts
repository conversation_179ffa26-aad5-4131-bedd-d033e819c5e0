import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    proxy: {
      // 匹配以/api/covers/开头的请求
      '/api/covers/': {
        target: 'http://localhost:9090',  // 后端地址
        changeOrigin: true,
        rewrite: (path) => path,  // /api/covers/books1.png --> 转发到后端的/api/covers/books1.png）
        // logLevel: 'debug' as any
      }
    }
  },
})
