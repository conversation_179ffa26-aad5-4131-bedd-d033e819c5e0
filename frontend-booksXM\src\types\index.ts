// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  role: 'USER' | 'ADMIN'
  createdAt: string
  updatedAt: string
  isActive?: boolean
  lastLoginAt?: string
  orderCount?: number
  totalAmount?: number
  avgOrderAmount?: number
  addresses?: Address[]
}

// 图书相关类型
export interface Book {
  id: number
  title: string
  author: string
  publisher: string
  isbn?: string
  category: string
  description?: string
  price: number
  originalPrice?: number
  stock: number
  coverUrl?: string
  publishDate?: string
  pageCount?: number
  language?: string
  rating: number
  ratingCount: number
  salesCount: number
  isFeatured: boolean
  createdAt: string
  updatedAt: string
}

// 购物车项类型
export interface CartItem {
  id: number
  bookId: number
  book: Book
  quantity: number
  selected: boolean
  userId: number
  createdAt: string
  updatedAt: string
}

// 订单相关类型
export interface Order {
  id: number
  orderNumber: string
  userId: number
  user: User
  status: 'PENDING' | 'PAID' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED'
  totalAmount: number
  shippingAddress: string
  contactPhone: string
  contactName: string
  orderItems: OrderItem[]
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: number
  orderId: number
  bookId: number
  book: Book
  quantity: number
  price: number
  createdAt: string
  updatedAt: string
}

// 地址类型
export interface Address {
  id: number
  userId: number
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PageResponse<T = any> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

export interface BooksResponse {
  books: Book[]
  total: number
  page: number
  size: number
  totalPages: number
}

// 登录相关类型
export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
}

// 搜索和筛选参数类型
export interface BookSearchParams {
  page?: number
  size?: number
  sortBy?: string
  sortDir?: 'asc' | 'desc'
  category?: string
  search?: string
  title?: string
  author?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
  featured?: boolean
}

// 购物车操作类型
export interface AddToCartRequest {
  bookId: number
  quantity: number
}

export interface UpdateCartItemRequest {
  quantity: number
}

export interface CartSummary {
  totalItems: number
  totalAmount: number
  selectedItems: number
  selectedAmount: number
}

// 订单创建类型
export interface CreateOrderRequest {
  cartItemIds: number[]
  shippingAddress: string
  contactPhone: string
  contactName: string
}

// 轮播图类型
export interface Banner {
  id: number
  title: string
  description: string
  image: string
  link?: string
}

// 统计数据类型
export interface OrderStats {
  totalOrders: number
  totalAmount: number
  pendingOrders: number
  completedOrders: number
}

// 错误类型
export interface ApiError {
  code: number
  message: string
  details?: any
}
