<template>
  <div class="book-management">
    <div class="page-header">
      <h1>图书管理</h1>
      <el-button type="primary" @click="handleAddBook">
        <el-icon>
          <Plus />
        </el-icon>
        添加图书
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input v-model="searchForm.keyword" placeholder="输入图书标题或作者" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item label="分类" class="Classification">
          <el-select v-model="searchForm.category" placeholder="选择分类" clearable>
            <el-option label="全部" value="" />
            <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" class="Status">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="有库存" value="in-stock" />
            <el-option label="缺货" value="out-of-stock" />
            <el-option label="推荐" value="featured" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 图书列表 -->
    <el-card class="table-card">
      <el-table :data="books" v-loading="loading" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />

        <el-table-column label="封面" width="80">
          <template #default="{ row }">
            <img :src="row.coverUrl || defaultCover" :alt="row.title" class="book-cover" />
          </template>
        </el-table-column>

        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="{ row }">
            <div class="book-title">{{ row.title }}</div>
            <div class="book-author">{{ row.author }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="category" label="分类" width="120" />

        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>

        <el-table-column prop="stock" label="库存" width="80">
          <template #default="{ row }">
            <el-tag :type="row.stock > 0 ? 'success' : 'danger'" size="small">
              {{ row.stock }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="salesCount" label="销量" width="80" />

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isFeatured" type="warning" size="small">推荐</el-tag>
            <el-tag v-else type="info" size="small">普通</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewBook(row)">
              查看
            </el-button>
            <el-button size="small" type="primary" @click="handleEditBook(row)">
              编辑
            </el-button>
            <el-button size="small" type="danger" @click="handleDeleteBook(row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedBooks.length > 0">
        <span>已选择 {{ selectedBooks.length }} 项</span>
        <el-button size="small" @click="handleBatchFeatured">批量推荐</el-button>
        <el-button size="small" @click="handleBatchUnfeatured">取消推荐</el-button>
        <el-button size="small" type="danger" @click="handleBatchDelete">批量删除</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>
    </el-card>

    <!-- 图书详情/编辑对话框 -->
    <el-dialog v-model="bookDialogVisible" :title="editingBook ? '编辑图书' : (viewingBook ? '图书详情' : '添加图书')" width="800px"
      :close-on-click-modal="false">
      <el-form ref="bookFormRef" :model="bookForm" :rules="bookRules" label-width="100px" :disabled="viewingBook">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-model="bookForm.title" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="作者" prop="author">
              <el-input v-model="bookForm.author" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出版社" prop="publisher">
              <el-input v-model="bookForm.publisher" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="ISBN" prop="isbn">
              <el-input v-model="bookForm.isbn" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分类" prop="category">
              <el-select v-model="bookForm.category" placeholder="选择分类">
                <el-option v-for="category in categories" :key="category" :label="category" :value="category" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="语言" prop="language">
              <el-input v-model="bookForm.language" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="bookForm.price" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原价" prop="originalPrice">
              <el-input-number v-model="bookForm.originalPrice" :min="0" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="库存" prop="stock">
              <el-input-number v-model="bookForm.stock" :min="0" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="页数" prop="pageCount">
              <el-input-number v-model="bookForm.pageCount" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出版日期" prop="publishDate">
              <el-date-picker v-model="bookForm.publishDate" type="date" placeholder="选择日期" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="封面" prop="coverUrl">
          <div class="cover-upload-section">
            <!-- 封面预览 -->
            <div class="cover-preview" v-if="bookForm.coverUrl">
              <img :src="bookForm.coverUrl" alt="封面预览" class="preview-image" />
            </div>

            <!-- 上传方式选择 -->
            <el-radio-group v-model="uploadMethod" class="upload-method-group">
              <el-radio label="url">输入URL</el-radio>
              <el-radio label="upload">上传文件</el-radio>
            </el-radio-group>

            <!-- URL输入 -->
            <div v-if="uploadMethod === 'url'" class="url-input-section">
              <el-input v-model="bookForm.coverUrl" placeholder="输入图片URL" />
            </div>

            <!-- 文件上传 -->
            <div v-if="uploadMethod === 'upload'" class="file-upload-section">
              <el-upload ref="uploadRef" :action="uploadAction" :headers="uploadHeaders" :before-upload="beforeUpload"
                :on-success="handleUploadSuccess" :on-error="handleUploadError" :show-file-list="false" accept="image/*"
                drag>
                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    只能上传jpg/png/gif/bmp/webp文件，且不超过5MB
                  </div>
                </template>
              </el-upload>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="简介" prop="description">
          <el-input v-model="bookForm.description" type="textarea" :rows="4" placeholder="输入图书简介" />
        </el-form-item>

        <el-form-item label="设置">
          <el-checkbox v-model="bookForm.isFeatured">推荐图书</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer v-if="!viewingBook">
        <el-button @click="bookDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveBook" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { bookAPI } from '@/api'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { UploadFilled } from '@element-plus/icons-vue'
  import { useUserStore } from '@/stores/user'
  import type { FormInstance, UploadProps } from 'element-plus'
  import type { Book } from '@/types'

  // 响应式数据
  const books = ref<Book[]>([])
  const categories = ref<string[]>([])
  const loading = ref(false)
  const saving = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(20)
  const total = ref(0)
  const selectedBooks = ref<Book[]>([])
  const bookDialogVisible = ref(false)
  const editingBook = ref<Book | null>(null)
  const viewingBook = ref(false)
  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=60&h=80&fit=crop')

  // 上传相关
  const uploadMethod = ref('url') // 'url' 或 'upload'
  const uploadRef = ref()
  const userStore = useUserStore()
  const uploadAction = ref('http://localhost:9090/api/uploads/image')
  const uploadHeaders = ref({
    'Authorization': `Bearer ${userStore.token}`
  })

  // 表单引用
  const bookFormRef = ref<FormInstance>()

  // 搜索表单
  const searchForm = ref({
    keyword: '',
    category: '',
    status: ''
  })

  // 图书表单
  const bookForm = ref({
    title: '',
    author: '',
    publisher: '',
    isbn: '',
    category: '',
    language: '中文',
    price: 0,
    originalPrice: 0,
    stock: 0,
    pageCount: 0,
    publishDate: '',
    coverUrl: '',
    description: '',
    isFeatured: false
  })

  // 表单验证规则
  const bookRules = {
    title: [{ required: true, message: '请输入图书标题', trigger: 'blur' }],
    author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
    publisher: [{ required: true, message: '请输入出版社', trigger: 'blur' }],
    category: [{ required: true, message: '请选择分类', trigger: 'change' }],
    price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
    stock: [{ required: true, message: '请输入库存', trigger: 'blur' }]
  }

  // 获取图书列表
  const fetchBooks = async () => {
    try {
      loading.value = true

      const params: any = {
        page: currentPage.value - 1,
        size: pageSize.value
      }

      if (searchForm.value.keyword) {
        params.search = searchForm.value.keyword
      }

      if (searchForm.value.category) {
        params.category = searchForm.value.category
      }

      if (searchForm.value.status) {
        switch (searchForm.value.status) {
          case 'in-stock':
            params.inStock = true
            break
          case 'out-of-stock':
            params.inStock = false
            break
          case 'featured':
            params.featured = true
            break
        }
      }

      const response = await bookAPI.getBooks(params)
      books.value = (response as any)?.books || response || []
      total.value = (response as any)?.total || books.value.length

    } catch (error) {
      console.error('获取图书列表失败:', error)
      ElMessage.error('获取图书列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await bookAPI.getCategories()
      categories.value = (response as any) || ['文学', '科技', '历史', '艺术', '教育', '生活']
    } catch (error) {
      console.error('获取分类失败:', error)
      categories.value = ['文学', '科技', '历史', '艺术', '教育', '生活']
    }
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    fetchBooks()
  }

  const handleReset = () => {
    searchForm.value = {
      keyword: '',
      category: '',
      status: ''
    }
    handleSearch()
  }

  // 分页处理
  const handlePageChange = (page: number) => {
    currentPage.value = page
    fetchBooks()
  }

  const handleSizeChange = (size: number) => {
    pageSize.value = size
    currentPage.value = 1
    fetchBooks()
  }

  // 选择处理
  const handleSelectionChange = (selection: Book[]) => {
    selectedBooks.value = selection
  }

  // 图书操作
  const handleAddBook = () => {
    editingBook.value = null
    viewingBook.value = false
    uploadMethod.value = 'url' // 上传方式
    bookForm.value = {
      title: '',
      author: '',
      publisher: '',
      isbn: '',
      category: '',
      language: '中文',
      price: 0,
      originalPrice: 0,
      stock: 0,
      pageCount: 0,
      publishDate: '',
      coverUrl: '',
      description: '',
      isFeatured: false
    }
    bookDialogVisible.value = true
  }

  const handleViewBook = (book: Book) => {
    editingBook.value = book
    viewingBook.value = true
    bookForm.value = {
      title: book.title,
      author: book.author,
      publisher: book.publisher,
      isbn: book.isbn || '',
      category: book.category,
      language: book.language || '中文',
      price: book.price,
      originalPrice: book.originalPrice || 0,
      stock: book.stock,
      pageCount: book.pageCount || 0,
      publishDate: book.publishDate || '',
      coverUrl: book.coverUrl || '',
      description: book.description || '',
      isFeatured: book.isFeatured || false
    }
    bookDialogVisible.value = true
  }

  const handleEditBook = (book: Book) => {
    editingBook.value = book
    viewingBook.value = false
    bookForm.value = {
      title: book.title,
      author: book.author,
      publisher: book.publisher,
      isbn: book.isbn || '',
      category: book.category,
      language: book.language || '中文',
      price: book.price,
      originalPrice: book.originalPrice || 0,
      stock: book.stock,
      pageCount: book.pageCount || 0,
      publishDate: book.publishDate || '',
      coverUrl: book.coverUrl || '',
      description: book.description || '',
      isFeatured: book.isFeatured || false
    }
    bookDialogVisible.value = true
  }

  const handleSaveBook = async () => {
    if (!bookFormRef.value) return

    try {
      await bookFormRef.value.validate()
      saving.value = true

      if (editingBook.value) {
        await bookAPI.updateBook(editingBook.value.id, bookForm.value)
        ElMessage.success('图书更新成功')
      } else {
        await bookAPI.createBook(bookForm.value)
        ElMessage.success('图书添加成功')
      }

      bookDialogVisible.value = false
      await fetchBooks()

    } catch (error) {
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  }

  // 文件上传相关方法
  const beforeUpload: UploadProps['beforeUpload'] = (file) => {
    // 检查文件类型
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      return false
    }

    // 检查文件大小
    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      ElMessage.error('图片大小不能超过 5MB!')
      return false
    }

    return true
  }

  const handleUploadSuccess = (response: any) => {
    if (response.fileUrl) {
      bookForm.value.coverUrl = 'http://localhost:9090' + response.fileUrl
      ElMessage.success('图片上传成功')
    } else {
      ElMessage.error('上传失败：' + (response.message || '未知错误'))
    }
  }

  const handleUploadError = (error: any) => {
    console.error('上传失败:', error)
    ElMessage.error('图片上传失败')
  }

  const handleDeleteBook = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这本图书吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await bookAPI.deleteBook(id)
      ElMessage.success('图书删除成功')
      await fetchBooks()

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 批量操作
  const handleBatchFeatured = async () => {
    ElMessage.info('批量推荐功能开发中...')
  }

  const handleBatchUnfeatured = async () => {
    ElMessage.info('批量取消推荐功能开发中...')
  }

  const handleBatchDelete = async () => {
    try {
      await ElMessageBox.confirm(`确定要删除选中的 ${selectedBooks.value.length} 本图书吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      ElMessage.info('批量删除功能开发中...')

    } catch (error) {
      // 用户取消操作
    }
  }

  // 工具函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  // 页面初始化
  onMounted(() => {
    fetchCategories()
    fetchBooks()
    // 滚动到顶部
    window.scrollTo({ top: 0 })
  })

</script>

<style scoped>
  .book-management {
    max-width: 1400px;
    margin: 0 auto;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .page-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .search-card {
    margin-bottom: 20px;
  }
  .Classification,.Status{
    width: 170px;
  }
  .table-card {
    margin-bottom: 20px;
  }

  .book-cover {
    width: 50px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
  }

  .book-title {
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
  }

  .book-author {
    font-size: 12px;
    color: #909399;
  }

  .batch-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    margin-top: 16px;
  }

  .pagination {
    display: flex;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
  }

  /* 封面上传相关样式 */
  .cover-upload-section {
    width: 100%;
  }

  .cover-preview {
    margin-bottom: 16px;
  }

  .preview-image {
    width: 120px;
    height: 160px;
    object-fit: cover;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
  }

  .upload-method-group {
    margin-bottom: 1px;
  }

  .url-input-section {
    width: 100%;
  }

  .file-upload-section {
    width: 100%;
  }

  .file-upload-section .el-upload-dragger {
    width: 100%;
    height: 120px;
  }

  @media (max-width: 768px) {
    .page-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .batch-actions {
      flex-wrap: wrap;
      gap: 8px;
    }
  }
</style>
