<template>
  <div class="admin-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px">
        <div class="admin-sidebar">
          <div class="admin-logo">
            <h2>管理后台</h2>
          </div>

          <el-menu :default-active="$route.path" router background-color="#304156" text-color="#bfcbd9"
            active-text-color="#409EFF">
            <el-menu-item index="/admin">
              <el-icon>
                <Odometer />
              </el-icon>
              <span>仪表盘</span>
            </el-menu-item>

            <el-menu-item index="/admin/books">
              <el-icon>
                <Reading />
              </el-icon>
              <span>图书管理</span>
            </el-menu-item>

            <el-menu-item index="/admin/orders">
              <el-icon>
                <Document />
              </el-icon>
              <span>订单管理</span>
            </el-menu-item>

            <el-menu-item index="/admin/users">
              <el-icon>
                <User />
              </el-icon>
              <span>用户管理</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-aside>

      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header height="60px">
          <div class="admin-header">
            <div class="header-left">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/admin' }">管理后台</el-breadcrumb-item>
                <el-breadcrumb-item>{{ getBreadcrumbTitle() }}</el-breadcrumb-item>
              </el-breadcrumb>
            </div>

            <div class="header-right">
              <el-dropdown @command="handleCommand">
                <span class="admin-user">
                  <el-icon>
                    <User />
                  </el-icon>
                  {{ userStore.user?.username || 'Admin' }}
                  <el-icon>
                    <ArrowDown />
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                    <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>

        <!-- 主内容 -->
        <el-main>
          <div class="admin-content">
            <router-view />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
  import { useRouter, useRoute } from 'vue-router'
  import { useUserStore } from '@/stores/user'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { onMounted } from 'vue'

  const router = useRouter()
  const route = useRoute()
  const userStore = useUserStore()

  // 获取面包屑标题
  const getBreadcrumbTitle = () => {
    const titleMap: Record<string, string> = {
      '/admin': '仪表盘',
      '/admin/books': '图书管理',
      '/admin/orders': '订单管理',
      '/admin/users': '用户管理'
    }
    return titleMap[route.path] || '管理后台'
  }

  // 处理下拉菜单命令
  const handleCommand = async (command: string) => {
    switch (command) {
      case 'profile':
        router.push('/profile')
        break
      case 'logout':
        try {
          await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })

          await userStore.logout()
          ElMessage.success('已退出登录')
          router.push('/login')

        } catch (error) {
          // 用户取消操作
        }
        break
    }
  }
  // 页面初始化
  onMounted(() => {
    // 滚动到顶部
    window.scrollTo({ top: 0 })
  })
</script>

<style scoped>
  .admin-layout {
    height: 100vh;
    overflow: hidden;
  }

  .admin-sidebar {
    height: 100vh;
    background-color: #304156;
  }

  .admin-logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2b3a4b;
    border-bottom: 1px solid #1f2d3d;
  }

  .admin-logo h2 {
    color: #fff;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }

  .el-menu {
    border-right: none;
    height: calc(100vh - 60px);
    overflow-y: auto;
  }

  .admin-header {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  }

  .header-left {
    flex: 1;
  }

  .header-right {
    display: flex;
    align-items: center;
  }

  .admin-user {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
    color: #606266;
  }

  .admin-user:hover {
    background-color: #f5f5f5;
  }

  .admin-content {
    height: calc(100vh - 60px);
    overflow-y: auto;
    padding: 20px;
    background: #f5f5f5;
  }

  @media (max-width: 768px) {
    .admin-layout {
      height: 100vh;
    }

    .el-aside {
      width: 200px !important;
    }

    .admin-content {
      padding: 16px;
    }

    .header-left {
      display: none;
    }
  }
</style>
