package com.booksxm.repository;

import com.booksxm.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    @Query("SELECT u FROM User u WHERE u.username = :usernameOrEmail OR u.email = :usernameOrEmail")
    Optional<User> findByUsernameOrEmail(@Param("usernameOrEmail") String usernameOrEmail);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 根据角色查找用户
     */
    List<User> findByRole(User.Role role);

    /**
     * 根据激活状态查找用户
     */
    List<User> findByIsActive(Boolean isActive);

    /**
     * 根据邮箱验证状态查找用户
     */
    List<User> findByEmailVerified(Boolean emailVerified);

    /**
     * 分页查询用户，支持按用户名、邮箱、角色筛选
     */
    @Query("SELECT u FROM User u WHERE " +
            "(:username IS NULL OR u.username LIKE %:username%) AND " +
            "(:email IS NULL OR u.email LIKE %:email%) AND " +
            "(:role IS NULL OR u.role = :role) AND " +
            "(:isActive IS NULL OR u.isActive = :isActive)")
    Page<User> findUsersWithFilters(@Param("username") String username,
            @Param("email") String email,
            @Param("role") User.Role role,
            @Param("isActive") Boolean isActive,
            Pageable pageable);

    /**
     * 统计指定时间段内注册的用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.createdAt BETWEEN :startDate AND :endDate")
    Long countUsersByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * 统计活跃用户数量
     */
    Long countByIsActiveTrue();

    /**
     * 统计已验证邮箱的用户数量
     */
    Long countByEmailVerifiedTrue();

    /**
     * 查找最近注册的用户
     */
    List<User> findTop10ByOrderByCreatedAtDesc();

    /**
     * 根据状态统计用户数量
     */
    Long countByIsActive(Boolean isActive);

    /**
     * 根据角色统计用户数量
     */
    Long countByRole(User.Role role);

    /**
     * 统计指定时间之后注册的用户数量
     */
    Long countByCreatedAtAfter(LocalDateTime date);
}
