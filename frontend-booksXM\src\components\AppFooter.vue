<template>
  <footer class="app-footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>BooksXM</h3>
          <p>专业的在线图书商城，为您提供优质的图书购买体验。</p>
          <div class="social-links">
            <el-button link size="small">
              <el-icon><Platform /></el-icon>
            </el-button>
            <el-button link size="small">
              <el-icon><ChatDotRound /></el-icon>
            </el-button>
            <el-button link size="small">
              <el-icon><Message /></el-icon>
            </el-button>
          </div>
        </div>
        
        <div class="footer-section">
          <h4>购物指南</h4>
          <ul>
            <li><a href="#">购物流程</a></li>
            <li><a href="#">支付方式</a></li>
            <li><a href="#">配送说明</a></li>
            <li><a href="#">退换货政策</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>客户服务</h4>
          <ul>
            <li><a href="#">联系我们</a></li>
            <li><a href="#">常见问题</a></li>
            <li><a href="#">意见反馈</a></li>
            <li><a href="#">售后服务</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>关于我们</h4>
          <ul>
            <li><a href="#">公司简介</a></li>
            <li><a href="#">招聘信息</a></li>
            <li><a href="#">合作伙伴</a></li>
            <li><a href="#">隐私政策</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <div class="copyright">
          <p>&copy; 2025 BooksXM. All rights reserved.</p>
        </div>
        <div class="links">
          <a href="#">服务条款</a>
          <span>|</span>
          <a href="#">隐私政策</a>
          <span>|</span>
          <a href="#">网站地图</a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// 组件逻辑
</script>

<style scoped>
.app-footer {
  background: #2c3e50;
  color: #ecf0f1;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  padding: 40px 0;
}

.footer-section h3 {
  color: #409EFF;
  font-size: 24px;
  margin-bottom: 16px;
  font-weight: 600;
}

.footer-section h4 {
  color: #ecf0f1;
  font-size: 16px;
  margin-bottom: 16px;
  font-weight: 500;
}

.footer-section p {
  color: #bdc3c7;
  line-height: 1.6;
  margin-bottom: 16px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
}

.footer-section li {
  margin-bottom: 8px;
  font-size: 12px;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: #409EFF;
}

.social-links {
  display: flex;
  gap: 8px;
}

.social-links .el-button {
  color: #bdc3c7;
  padding: 8px;
}

.social-links .el-button:hover {
  color: #409EFF;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copyright p {
  color: #95a5a6;
  margin: 0;
}

.links {
  display: flex;
  align-items: center;
  gap: 12px;
}

.links a {
  color: #95a5a6;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.links a:hover {
  color: #409EFF;
}

.links span {
  color: #7f8c8d;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .links {
    justify-content: center;
  }
}
</style>
