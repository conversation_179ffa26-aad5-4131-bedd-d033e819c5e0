<template>
  <div class="book-detail-page">
    <div class="container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="!book" class="error-container">
        <el-result icon="warning" title="图书不存在" sub-title="抱歉，您访问的图书不存在或已下架">
          <template #extra>
            <el-button type="primary" @click="$router.push('/books')">
              返回图书列表
            </el-button>
          </template>
        </el-result>
      </div>

      <div v-else class="book-detail">
        <!-- 面包屑导航 -->
        <el-breadcrumb separator="/" class="breadcrumb">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/books' }">图书</el-breadcrumb-item>
          <el-breadcrumb-item>{{ book.title }}</el-breadcrumb-item>
        </el-breadcrumb>

        <div class="book-content">
          <!-- 图书基本信息 -->
          <div class="book-main">
            <div class="book-cover">
              <img :src="book.coverUrl || defaultCover" :alt="book.title" />
              <div class="book-badges">
                <el-tag v-if="book.isFeatured" type="danger" size="small">推荐</el-tag>
                <el-tag v-if="book.stock <= 0" type="info" size="small">缺货</el-tag>
                <el-tag v-if="book.originalPrice && book.price < book.originalPrice" type="warning" size="small">
                  特价
                </el-tag>
              </div>
            </div>

            <div class="book-info">
              <h1 class="book-title">{{ book.title }}</h1>
              <div class="book-meta">
                <div class="meta-item">
                  <span class="label">作者：</span>
                  <span class="value">{{ book.author }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">出版社：</span>
                  <span class="value">{{ book.publisher }}</span>
                </div>
                <div class="meta-item" v-if="book.publishDate">
                  <span class="label">出版时间：</span>
                  <span class="value">{{ book.publishDate }}</span>
                </div>
                <div class="meta-item" v-if="book.isbn">
                  <span class="label">ISBN：</span>
                  <span class="value">{{ book.isbn }}</span>
                </div>
                <div class="meta-item">
                  <span class="label">分类：</span>
                  <span class="value">{{ book.category }}</span>
                </div>
                <div class="meta-item" v-if="book.pageCount">
                  <span class="label">页数：</span>
                  <span class="value">{{ book.pageCount }}页</span>
                </div>
              </div>

              <div class="book-rating" v-if="book.rating > 0">
                <el-rate v-model="book.rating" disabled show-score text-color="#ff9900" score-template="{value}" />
                <span class="rating-count">({{ book.ratingCount }}人评价)</span>
              </div>

              <div class="book-price">
                <span class="current-price">¥{{ book.price }}</span>
                <span v-if="book.originalPrice && book.price < book.originalPrice" class="original-price">
                  ¥{{ book.originalPrice }}
                </span>
              </div>

              <div class="book-stock">
                <span v-if="book.stock > 0" class="in-stock">
                  库存：{{ book.stock }}本
                </span>
                <span v-else class="out-of-stock">
                  暂时缺货
                </span>
              </div>

              <div class="book-actions">
                <div class="quantity-selector">
                  <span class="label">数量：</span>
                  <el-input-number v-model="quantity" :min="1" :max="book.stock" size="large"
                    :disabled="book.stock <= 0" />
                </div>

                <div class="action-buttons">
                  <el-button type="primary" size="large" :disabled="book.stock <= 0" @click="handleAddToCart"
                    :loading="addingToCart">
                    <el-icon>
                      <ShoppingCart />
                    </el-icon>
                    加入购物车
                  </el-button>

                  <el-button type="danger" size="large" :disabled="book.stock <= 0" @click="handleBuyNow">
                    立即购买
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 图书详细信息 -->
          <div class="book-details">
            <el-tabs v-model="activeTab" class="detail-tabs">
              <el-tab-pane value="图书简介" name="description">
                <div class="description-content">
                  <p v-if="book.description">{{ book.description }}</p>
                  <p v-else class="no-description">暂无图书简介</p>
                </div>
              </el-tab-pane>

              <el-tab-pane value="商品参数" name="params">
                <div class="params-content">
                  <table class="params-table">
                    <tbody>
                      <tr>
                        <td>书名</td>
                        <td>{{ book.title }}</td>
                      </tr>
                      <tr>
                        <td>作者</td>
                        <td>{{ book.author }}</td>
                      </tr>
                      <tr>
                        <td>出版社</td>
                        <td>{{ book.publisher }}</td>
                      </tr>
                      <tr v-if="book.publishDate">
                        <td>出版时间</td>
                        <td>{{ book.publishDate }}</td>
                      </tr>
                      <tr v-if="book.isbn">
                        <td>ISBN</td>
                        <td>{{ book.isbn }}</td>
                      </tr>
                      <tr>
                        <td>分类</td>
                        <td>{{ book.category }}</td>
                      </tr>
                      <tr v-if="book.pageCount">
                        <td>页数</td>
                        <td>{{ book.pageCount }}页</td>
                      </tr>
                      <tr>
                        <td>语言</td>
                        <td>{{ book.language || '中文' }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>

        <!-- 相关推荐 -->
        <!-- <div class="related-books" v-if="relatedBooks.length > 0">
          <h3 class="section-title">相关推荐</h3>
          <div class="books-grid">
            <BookCard v-for="relatedBook in relatedBooks" :key="relatedBook.id" :book="relatedBook"
              @add-to-cart="handleRelatedAddToCart" />
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useCartStore } from '@/stores/cart'
  import { bookAPI } from '@/api'
  import { ElMessage } from 'element-plus'
  import BookCard from '@/components/BookCard.vue'
  import type { Book } from '@/types'

  const route = useRoute()
  const router = useRouter()
  const cartStore = useCartStore()

  const book = ref<Book | null>(null)
  const relatedBooks = ref<Book[]>([])
  const loading = ref(false)
  const addingToCart = ref(false)
  const quantity = ref(1)
  const activeTab = ref('description')
  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=400&h=500&fit=crop')

  // 获取图书详情
  const fetchBookDetail = async () => {
    try {
      loading.value = true
      const bookId = parseInt(route.params.id as string)

      if (isNaN(bookId)) {
        throw new Error('无效的图书ID')
      }

      const response = await bookAPI.getBook(bookId)
      book.value = response.data || response

      // 获取相关推荐
      await fetchRelatedBooks()

    } catch (error) {
      console.error('获取图书详情失败:', error)
      ElMessage.error('获取图书详情失败')
    } finally {
      loading.value = false
    }
  }

  // 获取相关推荐
  const fetchRelatedBooks = async () => {
    try {
      if (!book.value) return

      const response = await bookAPI.getBooks({
        category: book.value.category,
        size: 4
      })

      // 过滤掉当前图书
      relatedBooks.value = (response.data.content || []).filter((b: any) => b.id !== book.value?.id)
    } catch (error) {
      console.error('获取相关推荐失败:', error)
    }
  }

  // 添加到购物车
  const handleAddToCart = async () => {
    if (!book.value) return
    try {
      addingToCart.value = true
      await cartStore.addItem(book.value, quantity.value)
      ElMessage.success(`已添加 ${quantity.value} 本到购物车`)
    } catch (error) {
      ElMessage.error('添加到购物车失败')
    } finally {
      addingToCart.value = false
    }
  }

  // 立即购买
  const handleBuyNow = async () => {
    if (!book.value) return
    try {
      await cartStore.addItem(book.value, quantity.value)
      router.push('/cart')
    } catch (error) {
      ElMessage.error('操作失败')
    }
  }

  // 相关推荐添加到购物车
  const handleRelatedAddToCart = async (relatedBook: Book) => {
    try {
      await cartStore.addItem(relatedBook, 1)
      ElMessage.success('已添加到购物车')
    } catch (error) {
      ElMessage.error('添加到购物车失败')
    }
  }

  // 页面初始化
  onMounted(() => {
    fetchBookDetail()
    // 滚动到顶部
    window.scrollTo({ top: 0 })
  })

</script>

<style scoped>
  .book-detail-page {
    min-height: 100vh;
    padding: 20px 0;
  }

  .loading-container,
  .error-container {
    padding: 60px 20px;
  }

  .breadcrumb {
    margin-bottom: 24px;
  }

  .book-content {
    background: white;
    border-radius: 8px;
    padding: 32px;
    margin-bottom: 32px;
  }

  .book-main {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .book-cover {
    position: relative;
  }

  .book-cover img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .book-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .book-title {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 24px;
    line-height: 1.4;
  }

  .book-meta {
    margin-bottom: 20px;
  }

  .meta-item {
    display: flex;
    margin-bottom: 8px;
  }

  .meta-item .label {
    width: 80px;
    color: #909399;
    font-size: 14px;
  }

  .meta-item .value {
    color: #606266;
    font-size: 14px;
  }

  .book-rating {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
  }

  .rating-count {
    font-size: 14px;
    color: #909399;
  }

  .book-price {
    margin-bottom: 16px;
  }

  .current-price {
    font-size: 26px;
    font-weight: 600;
    color: #FF2832;
    margin-right: 16px;
    margin-block-start: 1em;
  }

  .original-price {
    font-size: 18px;
    color: #909399;
    text-decoration: line-through;
  }

  .book-stock {
    margin-bottom: 24px;
    font-size: 14px;
  }

  .in-stock {
    color: #67c23a;
  }

  .out-of-stock {
    color: #f56c6c;
  }

  .book-actions {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .quantity-selector {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .quantity-selector .label {
    font-size: 16px;
    color: #606266;
  }

  .action-buttons {
    display: flex;
    gap: 16px;
  }

  .action-buttons .el-button {
    height: 48px;
    padding: 0 32px;
    font-size: 16px;
    font-weight: 500;
  }

  .book-details {
    border-top: 1px solid #f0f0f0;
    padding-top: 32px;
  }

  .detail-tabs {
    margin-top: 16px;
  }

  .description-content {
    padding: 20px 0;
    line-height: 1.8;
    color: #606266;
  }

  .no-description {
    color: #c0c4cc;
    font-style: italic;
  }

  .params-table {
    width: 100%;
    border-collapse: collapse;
  }

  .params-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .params-table td:first-child {
    width: 120px;
    background: #fafafa;
    color: #909399;
    font-weight: 500;
  }

  .related-books {
    background: white;
    border-radius: 8px;
    padding: 32px;
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 24px;
  }

  .books-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }

  @media (max-width: 768px) {
    .book-content {
      padding: 20px;
    }

    .book-main {
      grid-template-columns: 1fr;
      gap: 24px;
    }

    .book-cover {
      max-width: 300px;
      margin: 0 auto;
    }

    .book-title {
      font-size: 24px;
    }

    .action-buttons {
      flex-direction: column;
    }

    .books-grid {
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 16px;
    }
  }
</style>
