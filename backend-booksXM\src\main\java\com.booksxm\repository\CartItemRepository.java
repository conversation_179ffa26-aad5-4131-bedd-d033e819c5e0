package com.booksxm.repository;

import com.booksxm.entity.Book;
import com.booksxm.entity.CartItem;
import com.booksxm.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CartItemRepository extends JpaRepository<CartItem, Long> {

    /**
     * 根据用户查找购物车项目
     */
    List<CartItem> findByUser(User user);

    /**
     * 根据用户ID查找购物车项目
     */
    List<CartItem> findByUserId(Long userId);

    /**
     * 根据用户ID查找购物车项目（包含Book数据）
     */
    @Query("SELECT c FROM CartItem c JOIN FETCH c.book WHERE c.user.id = :userId")
    List<CartItem> findByUserIdWithBook(@Param("userId") Long userId);

    /**
     * 根据ID查找购物车项目（包含Book数据）
     */
    @Query("SELECT c FROM CartItem c JOIN FETCH c.book WHERE c.id = :id")
    Optional<CartItem> findByIdWithBook(@Param("id") Long id);

    /**
     * 根据用户ID和选中状态查找购物车项目（包含Book数据）
     */
    @Query("SELECT c FROM CartItem c JOIN FETCH c.book WHERE c.user.id = :userId AND c.isSelected = :isSelected")
    List<CartItem> findByUserIdAndIsSelectedWithBook(@Param("userId") Long userId,
            @Param("isSelected") Boolean isSelected);

    /**
     * 根据用户和图书查找购物车项目
     */
    Optional<CartItem> findByUserAndBook(User user, Book book);

    /**
     * 根据用户ID和图书ID查找购物车项目
     */
    Optional<CartItem> findByUserIdAndBookId(Long userId, Long bookId);

    /**
     * 根据用户查找选中的购物车项目
     */
    List<CartItem> findByUserAndIsSelected(User user, Boolean isSelected);

    /**
     * 根据用户ID查找选中的购物车项目
     */
    List<CartItem> findByUserIdAndIsSelected(Long userId, Boolean isSelected);

    /**
     * 统计用户购物车中的商品数量
     */
    Long countByUser(User user);

    /**
     * 统计用户购物车中选中商品的数量
     */
    Long countByUserAndIsSelected(User user, Boolean isSelected);

    /**
     * 统计用户购物车中商品的总数量（包括每个商品的数量）
     */
    @Query("SELECT COALESCE(SUM(c.quantity), 0) FROM CartItem c WHERE c.user = :user")
    Integer getTotalQuantityByUser(@Param("user") User user);

    /**
     * 统计用户购物车中选中商品的总数量
     */
    @Query("SELECT COALESCE(SUM(c.quantity), 0) FROM CartItem c WHERE c.user = :user AND c.isSelected = true")
    Integer getSelectedTotalQuantityByUser(@Param("user") User user);

    /**
     * 删除用户的所有购物车项目
     */
    @Modifying
    @Query("DELETE FROM CartItem c WHERE c.user = :user")
    void deleteByUser(@Param("user") User user);

    /**
     * 删除用户选中的购物车项目
     */
    @Modifying
    @Query("DELETE FROM CartItem c WHERE c.user = :user AND c.isSelected = true")
    void deleteByUserAndIsSelected(@Param("user") User user);

    /**
     * 删除指定图书的所有购物车项目
     */
    @Modifying
    @Query("DELETE FROM CartItem c WHERE c.book = :book")
    void deleteByBook(@Param("book") Book book);

    /**
     * 更新用户所有购物车项目的选中状态
     */
    @Modifying
    @Query("UPDATE CartItem c SET c.isSelected = :isSelected WHERE c.user = :user")
    void updateSelectionByUser(@Param("user") User user, @Param("isSelected") Boolean isSelected);

    /**
     * 查找包含指定图书的购物车项目
     */
    List<CartItem> findByBook(Book book);

    /**
     * 查找用户购物车中的有效商品（图书仍然可用且有库存）
     */
    @Query("SELECT c FROM CartItem c WHERE c.user = :user AND c.book.isActive = true AND c.book.stock >= c.quantity")
    List<CartItem> findValidCartItemsByUser(@Param("user") User user);

    /**
     * 查找用户购物车中的无效商品（图书不可用或库存不足）
     */
    @Query("SELECT c FROM CartItem c WHERE c.user = :user AND (c.book.isActive = false OR c.book.stock < c.quantity)")
    List<CartItem> findInvalidCartItemsByUser(@Param("user") User user);
}
