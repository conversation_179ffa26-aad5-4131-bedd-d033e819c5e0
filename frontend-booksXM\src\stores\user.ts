import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authAPI } from '@/api'

export interface User {
  id: number
  username: string
  email: string
  role: 'USER' | 'ADMIN'
  avatar?: string
  createdAt: string
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))

  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')

  // 登录
  const login = async (credentials: { username: string; password: string }) => {
    try {
      const response = await authAPI.login(credentials)
      console.log('Login response:', response) // 调试信息

      // 检查响应结构并适配
      let tokenValue: string
      let userData: any

      if (response.data && response.data.token) {
        // 如果响应有data字段
        tokenValue = response.data.token
        userData = response.data.user
      } else if ((response as any).token) {
        // 如果响应直接是数据
        tokenValue = (response as any).token
        userData = (response as any).user
      } else {
        throw new Error('无法从响应中获取token')
      }

      if (!tokenValue) {
        throw new Error('登录响应中缺少token')
      }

      token.value = tokenValue
      user.value = userData
      localStorage.setItem('token', tokenValue)
      localStorage.setItem('user', JSON.stringify(userData))
      return response
    } catch (error) {
      console.error('Login error:', error) // 调试信息
      throw error
    }
  }

  // 注册
  const register = async (userData: { username: string; email: string; password: string }) => {
    try {
      const response = await authAPI.register(userData)
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      token.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  }

  // 刷新令牌
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken()

      let tokenValue: string
      if (response.data && response.data.token) {
        tokenValue = response.data.token
      } else if ((response as any).token) {
        tokenValue = (response as any).token
      } else {
        throw new Error('无法从响应中获取token')
      }

      token.value = tokenValue
      localStorage.setItem('token', tokenValue)
      return response
    } catch (error) {
      logout()
      throw error
    }
  }

  // 初始化用户信息
  const initUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user:', error)
        logout()
      }
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    isAdmin,
    login,
    register,
    logout,
    refreshToken,
    initUser
  }
})
