package com.booksxm.controller;

import com.booksxm.entity.CartItem;
import com.booksxm.service.AuthService;
import com.booksxm.service.CartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/cart")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CartController {

    private static final Logger logger = LoggerFactory.getLogger(CartController.class);

    @Autowired
    private CartService cartService;

    @Autowired
    private AuthService authService;

    /**
     * 获取用户购物车
     */
    @GetMapping
    public ResponseEntity<?> getCart() {
        try {
            Long userId = authService.getCurrentUser().getId();
            logger.info("获取用户购物车，用户ID: {}", userId);

            List<CartItem> cartItems = cartService.getUserCart(userId);
            logger.info("查询到购物车项目数量: {}", cartItems.size());

            // 打印每个购物车项目的详细信息
            for (CartItem item : cartItems) {
                logger.info("购物车项目: ID={}, BookID={}, BookTitle={}, Quantity={}, IsSelected={}",
                        item.getId(), item.getBook().getId(), item.getBook().getTitle(),
                        item.getQuantity(), item.getIsSelected());
            }

            // 基于已获取的cartItems计算summary，避免重复查询
            CartService.CartSummary summary = cartService.calculateCartSummary(cartItems);

            Map<String, Object> response = new HashMap<>();
            response.put("items", cartItems);
            response.put("summary", summary);

            logger.info("返回购物车响应: items={}, summary={}", cartItems.size(), summary.getTotalItems());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取购物车失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取购物车失败: " + e.getMessage()));
        }
    }

    /**
     * 添加商品到购物车
     */
    @PostMapping("/add")
    public ResponseEntity<?> addToCart(@RequestBody AddToCartRequest request) {
        try {
            Long userId = authService.getCurrentUser().getId();
            CartItem cartItem = cartService.addToCart(userId, request.getBookId(), request.getQuantity());

            Map<String, Object> response = new HashMap<>();
            response.put("message", "添加到购物车成功");
            response.put("cartItem", cartItem);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("添加到购物车失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    /**
     * 更新购物车商品数量
     */
    @PutMapping("/{cartItemId}")
    public ResponseEntity<?> updateCartItem(@PathVariable Long cartItemId,
            @RequestBody UpdateCartItemRequest request) {
        try {
            Long userId = authService.getCurrentUser().getId();
            CartItem cartItem = cartService.updateCartItem(userId, cartItemId, request.getQuantity());

            Map<String, Object> response = new HashMap<>();
            if (cartItem != null) {
                response.put("message", "更新购物车成功");
                response.put("cartItem", cartItem);
            } else {
                response.put("message", "商品已从购物车中移除");
            }

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("更新购物车失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    /**
     * 删除购物车商品
     */
    @DeleteMapping("/{cartItemId}")
    public ResponseEntity<?> removeFromCart(@PathVariable Long cartItemId) {
        try {
            Long userId = authService.getCurrentUser().getId();
            cartService.removeFromCart(userId, cartItemId);

            return ResponseEntity.ok(Map.of("message", "删除成功"));
        } catch (Exception e) {
            logger.error("删除购物车项失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    /**
     * 清空购物车
     */
    @DeleteMapping
    public ResponseEntity<?> clearCart() {
        try {
            Long userId = authService.getCurrentUser().getId();
            cartService.clearCart(userId);

            return ResponseEntity.ok(Map.of("message", "购物车已清空"));
        } catch (Exception e) {
            logger.error("清空购物车失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    /**
     * 更新商品选中状态
     */
    @PutMapping("/{cartItemId}/selection")
    public ResponseEntity<?> updateSelection(@PathVariable Long cartItemId,
            @RequestBody UpdateSelectionRequest request) {
        try {
            Long userId = authService.getCurrentUser().getId();
            CartItem cartItem = cartService.updateSelection(userId, cartItemId, request.getSelected());

            Map<String, Object> response = new HashMap<>();
            response.put("message", "更新选中状态成功");
            response.put("cartItem", cartItem);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("更新选中状态失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    /**
     * 全选/取消全选
     */
    @PutMapping("/select-all")
    public ResponseEntity<?> selectAll(@RequestBody UpdateSelectionRequest request) {
        try {
            Long userId = authService.getCurrentUser().getId();
            cartService.updateAllSelection(userId, request.getSelected());

            return ResponseEntity.ok(Map.of("message", "更新全选状态成功"));
        } catch (Exception e) {
            logger.error("更新全选状态失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    /**
     * 获取购物车统计信息
     */
    @GetMapping("/summary")
    public ResponseEntity<?> getCartSummary() {
        try {
            Long userId = authService.getCurrentUser().getId();
            CartService.CartSummary summary = cartService.getCartSummary(userId);

            return ResponseEntity.ok(summary);
        } catch (Exception e) {
            logger.error("获取购物车统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", e.getMessage()));
        }
    }

    // 请求DTO类
    public static class AddToCartRequest {
        private Long bookId;
        private Integer quantity = 1;

        public Long getBookId() {
            return bookId;
        }

        public void setBookId(Long bookId) {
            this.bookId = bookId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }

    public static class UpdateCartItemRequest {
        private Integer quantity;

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }

    public static class UpdateSelectionRequest {
        private Boolean selected;

        public Boolean getSelected() {
            return selected;
        }

        public void setSelected(Boolean selected) {
            this.selected = selected;
        }
    }
}
