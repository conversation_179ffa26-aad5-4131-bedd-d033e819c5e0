package com.booksxm.controller;

import com.booksxm.service.FileStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/uploads")
@CrossOrigin(origins = "*", maxAge = 3600)
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);
    
    // 允许的图片文件类型
    private static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
    );
    
    // 最大文件大小 (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;

    @Autowired
    private FileStorageService fileStorageService;

    /**
     * 上传图片文件（管理员功能）
     */
    @PostMapping("/image")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> uploadImage(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件是否为空
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("message", "请选择要上传的文件"));
            }
            
            // 验证文件大小
            if (file.getSize() > MAX_FILE_SIZE) {
                return ResponseEntity.badRequest()
                    .body(Map.of("message", "文件大小不能超过5MB"));
            }
            
            // 验证文件类型
            String contentType = file.getContentType();
            if (contentType == null || !ALLOWED_IMAGE_TYPES.contains(contentType.toLowerCase())) {
                return ResponseEntity.badRequest()
                    .body(Map.of("message", "只支持图片文件格式 (JPEG, PNG, GIF, BMP, WebP)"));
            }
            
            // 存储文件
            String fileName = fileStorageService.storeFile(file);
            String fileUrl = fileStorageService.getFileUrl(fileName);
            
            logger.info("图片上传成功: {}", fileName);
            
            return ResponseEntity.ok(Map.of(
                "message", "文件上传成功",
                "fileName", fileName,
                "fileUrl", fileUrl,
                "originalName", file.getOriginalFilename(),
                "size", file.getSize(),
                "contentType", contentType
            ));
            
        } catch (Exception e) {
            logger.error("文件上传失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("message", "文件上传失败: " + e.getMessage()));
        }
    }

    /**
     * 下载/访问上传的文件
     */
    @GetMapping("/{fileName:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName, HttpServletRequest request) {
        try {
            // 加载文件作为Resource
            Resource resource = fileStorageService.loadFileAsResource(fileName);

            // 尝试确定文件的内容类型
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
            } catch (IOException ex) {
                logger.info("无法确定文件类型");
            }

            // 如果无法确定内容类型，则使用默认值
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
                
        } catch (Exception e) {
            logger.error("文件下载失败: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }
}
