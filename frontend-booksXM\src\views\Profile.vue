<template>
  <div class="profile-page">
    <div class="container">
      <div class="profile-header">
        <h1>个人中心</h1>
      </div>

      <div class="profile-content">
        <div class="profile-sidebar">
          <el-menu :default-active="activeTab" mode="vertical" @select="handleTabChange">
            <el-menu-item index="info">
              <el-icon>
                <User />
              </el-icon>
              <span>个人信息</span>
            </el-menu-item>
            <el-menu-item index="password">
              <el-icon>
                <Lock />
              </el-icon>
              <span>修改密码</span>
            </el-menu-item>
            <el-menu-item index="addresses">
              <el-icon>
                <Location />
              </el-icon>
              <span>收货地址</span>
            </el-menu-item>
          </el-menu>
        </div>

        <div class="profile-main">
          <!-- 个人信息 -->
          <div v-if="activeTab === 'info'" class="tab-content">
            <el-card header="个人信息">
              <el-form ref="infoFormRef" :model="userInfo" :rules="infoRules" label-width="100px">
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="userInfo.username" disabled />
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="userInfo.email" />
                </el-form-item>
                <el-form-item label="手机号" prop="phoneNumber">
                  <el-input v-model="userInfo.phoneNumber" />
                </el-form-item>
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="userInfo.realName" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleUpdateInfo" :loading="updating">
                    保存修改
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- 修改密码 -->
          <div v-if="activeTab === 'password'" class="tab-content">
            <el-card header="修改密码">
              <el-form ref="passwordFormRef" :model="passwordForm" :rules="passwordRules" label-width="100px">
                <el-form-item label="当前密码" prop="oldPassword">
                  <el-input v-model="passwordForm.oldPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input v-model="passwordForm.newPassword" type="password" show-password />
                </el-form-item>
                <el-form-item label="确认密码" prop="confirmPassword">
                  <el-input v-model="passwordForm.confirmPassword" type="password" show-password />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleChangePassword" :loading="changingPassword">
                    修改密码
                  </el-button>
                </el-form-item>
              </el-form>
            </el-card>
          </div>

          <!-- 收货地址 -->
          <div v-if="activeTab === 'addresses'" class="tab-content">
            <el-card>
              <template #header>
                <div class="card-header">
                  <span>收货地址</span>
                  <el-button type="primary" @click="handleAddAddress">
                    添加地址
                  </el-button>
                </div>
              </template>

              <div v-if="addresses.length === 0" class="empty-addresses">
                <el-empty description="暂无收货地址" />
              </div>

              <div v-else class="addresses-list">
                <div v-for="address in addresses" :key="address.id" class="address-item">
                  <div class="address-info">
                    <div class="address-header">
                      <span class="name">{{ address.name }}</span>
                      <span class="phone">{{ address.phoneNumber }}</span>
                      <el-tag v-if="address.isDefault" type="success" size="small">
                        默认
                      </el-tag>
                    </div>
                    <div class="address-detail">
                      {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
                    </div>
                  </div>
                  <div class="address-actions">
                    <el-button v-if="!address.isDefault" size="small" @click="handleSetDefault(address.id)">
                      设为默认
                    </el-button>
                    <el-button size="small" @click="handleEditAddress(address)">
                      编辑
                    </el-button>
                    <el-button size="small" type="danger" @click="handleDeleteAddress(address.id)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 地址编辑对话框 -->
    <el-dialog v-model="addressDialogVisible" :title="editingAddress ? '编辑地址' : '添加地址'" width="500px">
      <el-form ref="addressFormRef" :model="addressForm" :rules="addressRules" label-width="80px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="addressForm.name" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="addressForm.phone" />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="addressForm.province" />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="addressForm.city" />
        </el-form-item>
        <el-form-item label="区县" prop="district">
          <el-input v-model="addressForm.district" />
        </el-form-item>
        <el-form-item label="详细地址" prop="detail">
          <el-input v-model="addressForm.detail" type="textarea" :rows="3" />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="addressForm.isDefault">
            设为默认地址
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="addressDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveAddress" :loading="savingAddress">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { useUserStore } from '@/stores/user'
  import { userAPI } from '@/api'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import type { FormInstance } from 'element-plus'

  const userStore = useUserStore()

  // 响应式数据
  const activeTab = ref('info')
  const updating = ref(false)
  const changingPassword = ref(false)
  const savingAddress = ref(false)
  const addressDialogVisible = ref(false)
  const editingAddress = ref<any>(null)

  // 表单引用
  const infoFormRef = ref<FormInstance>()
  const passwordFormRef = ref<FormInstance>()
  const addressFormRef = ref<FormInstance>()

  // 用户信息
  const userInfo = ref({
    username: '',
    email: '',
    phoneNumber: '',
    realName: ''
  })

  // 密码表单
  const passwordForm = ref({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  // 地址列表
  const addresses = ref<any[]>([])

  // 地址表单
  const addressForm = ref({
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    isDefault: false
  })

  // 表单验证规则
  const infoRules = {
    email: [
      { required: true, message: '请输入邮箱', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
    ],
    phoneNumber: [
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ]
  }

  const passwordRules = {
    oldPassword: [
      { required: true, message: '请输入当前密码', trigger: 'blur' }
    ],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, message: '请确认新密码', trigger: 'blur' },
      {
        validator: (rule: any, value: string, callback: Function) => {
          if (value !== passwordForm.value.newPassword) {
            callback(new Error('两次输入的密码不一致'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  const addressRules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
    province: [{ required: true, message: '请输入省份', trigger: 'blur' }],
    city: [{ required: true, message: '请输入城市', trigger: 'blur' }],
    district: [{ required: true, message: '请输入区县', trigger: 'blur' }],
    detail: [{ required: true, message: '请输入详细地址', trigger: 'blur' }]
  }

  // 切换标签
  const handleTabChange = (key: string) => {
    activeTab.value = key
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      const response = await userAPI.getUserInfo()
      console.log('API响应数据:', response)  // 调试信息

      // 从响应中获取实际数据
      const userData = response.data || response
      console.log('用户数据:', userData)  // 调试信息

      // 确保 userInfo 有默认值，避免 undefined 错误
      if (userData && typeof userData === 'object') {
        userInfo.value = {
          username: userData.username || '',
          email: userData.email || '',
          phoneNumber: userData.phoneNumber || '',
          realName: userData.realName || ''
        }
      } else {
        console.error('API返回的数据格式不正确:', userData)
        // 设置默认值避免渲染错误
        userInfo.value = {
          username: '',
          email: '',
          phoneNumber: '',
          realName: ''
        }
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 设置默认值避免渲染错误
      userInfo.value = {
        username: '',
        email: '',
        phoneNumber: '',
        realName: ''
      }
    }
  }

  // 更新用户信息
  const handleUpdateInfo = async () => {
    if (!infoFormRef.value) return

    try {
      await infoFormRef.value.validate()
      updating.value = true

      await userAPI.updateUserInfo(userInfo.value)
      ElMessage.success('个人信息更新成功')

    } catch (error) {
      ElMessage.error('更新失败')
    } finally {
      updating.value = false
    }
  }

  // 修改密码
  const handleChangePassword = async () => {
    if (!passwordFormRef.value) return

    try {
      await passwordFormRef.value.validate()
      changingPassword.value = true

      await userAPI.changePassword({
        oldPassword: passwordForm.value.oldPassword,
        newPassword: passwordForm.value.newPassword
      })

      ElMessage.success('密码修改成功')
      passwordForm.value = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }

    } catch (error) {
      ElMessage.error('密码修改失败')
    } finally {
      changingPassword.value = false
    }
  }

  // 获取地址列表
  const fetchAddresses = async () => {
    try {
      const response = await userAPI.getAddresses()
      console.log('地址API响应数据:', response)  // 调试信息

      // 从响应中获取实际数据
      const addressData = response.data || response
      console.log('地址数据:', addressData)  // 调试信息

      addresses.value = Array.isArray(addressData) ? addressData : []
    } catch (error) {
      console.error('获取地址列表失败:', error)
      addresses.value = []
    }
  }

  // 添加地址
  const handleAddAddress = () => {
    editingAddress.value = null
    addressForm.value = {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    }
    addressDialogVisible.value = true
  }

  // 编辑地址
  const handleEditAddress = (address: any) => {
    editingAddress.value = address
    addressForm.value = { ...address }
    addressDialogVisible.value = true
  }

  // 保存地址
  const handleSaveAddress = async () => {
    if (!addressFormRef.value) return

    try {
      await addressFormRef.value.validate()
      savingAddress.value = true

      console.log('保存地址数据:', addressForm.value)  // 调试信息

      if (editingAddress.value) {
        await userAPI.updateAddress(editingAddress.value.id, addressForm.value)
        ElMessage.success('地址更新成功')
      } else {
        await userAPI.addAddress(addressForm.value)
        ElMessage.success('地址添加成功')
      }

      addressDialogVisible.value = false
      await fetchAddresses()

    } catch (error) {
      console.error('保存地址失败:', error)  // 调试信息
      ElMessage.error('保存失败')
    } finally {
      savingAddress.value = false
    }
  }

  // 设为默认地址
  const handleSetDefault = async (id: number) => {
    try {
      await userAPI.updateAddress(id, { isDefault: true })
      ElMessage.success('已设为默认地址')
      await fetchAddresses()
    } catch (error) {
      ElMessage.error('设置失败')
    }
  }

  // 删除地址
  const handleDeleteAddress = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这个地址吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await userAPI.deleteAddress(id)
      ElMessage.success('地址删除成功')
      await fetchAddresses()

    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 页面初始化
  onMounted(() => {
    fetchUserInfo()
    fetchAddresses()
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  })
</script>

<style scoped>
  .profile-page {
    min-height: 100vh;
    background: #f5f5f5;
    padding: 20px 0;
  }

  .profile-header {
    margin-bottom: 20px;
  }

  .profile-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0;
  }

  .profile-content {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 20px;
  }

  .profile-sidebar {
    overflow: hidden;
  }

  .profile-sidebar,
  .profile-main {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .tab-content {
    padding: 24px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .empty-addresses {
    padding: 40px 20px;
    text-align: center;
  }

  .addresses-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .address-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
  }

  .address-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }

  .address-header .name {
    font-weight: 600;
    color: #303133;
  }

  .address-header .phone {
    color: #606266;
  }

  .address-detail {
    color: #606266;
    line-height: 1.5;
  }

  .address-actions {
    display: flex;
    gap: 8px;
  }

  @media (max-width: 768px) {
    .profile-content {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .profile-sidebar {
      order: 2;
    }

    .profile-main {
      order: 1;
    }

    .tab-content {
      padding: 16px;
    }

    .address-item {
      flex-direction: column;
      gap: 12px;
    }

    .address-actions {
      align-self: stretch;
      justify-content: flex-end;
    }
  }
</style>
