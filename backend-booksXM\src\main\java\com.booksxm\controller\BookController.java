package com.booksxm.controller;

import com.booksxm.entity.Book;
import com.booksxm.service.BookService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
@RestController
@RequestMapping("/books")
@CrossOrigin(origins = "*", maxAge = 3600)
public class BookController {

    private static final Logger logger = LoggerFactory.getLogger(BookController.class);

    @Autowired
    private BookService bookService;
    

    /**
     * 获取图书列表（分页）
     */
    @GetMapping
    public ResponseEntity<?> getBooks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String author,
            @RequestParam(required = false) BigDecimal minPrice,
            @RequestParam(required = false) BigDecimal maxPrice,
            @RequestParam(required = false) Boolean inStock) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<Book> books;

            if (search != null && !search.trim().isEmpty()) {
                // 搜索模式
                books = bookService.searchBooks(search.trim(), pageable);
            } else if (title != null || author != null ||
                    minPrice != null || maxPrice != null || inStock != null) {
                // 高级筛选模式
                books = bookService.searchBooksWithFilters(title, author, category,
                        minPrice, maxPrice, inStock, pageable);
            } else if (category != null && !category.trim().isEmpty()) {
                // 分类筛选模式
                books = bookService.getBooksByCategory(category.trim(), pageable);
            } else {
                // 默认获取所有图书
                books = bookService.getAllBooks(pageable);
            }

            Map<String, Object> response = new HashMap<>();
            response.put("books", books.getContent());
            response.put("currentPage", books.getNumber());
            response.put("totalItems", books.getTotalElements());
            response.put("totalPages", books.getTotalPages());
            response.put("hasNext", books.hasNext());
            response.put("hasPrevious", books.hasPrevious());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("获取图书列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取图书列表失败: " + e.getMessage()));
        }
    }

    /**
     * 根据ID获取图书详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getBookById(@PathVariable Long id) {
        try {
            Optional<Book> book = bookService.getBookById(id);
            if (book.isPresent()) {
                return ResponseEntity.ok(book.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取图书详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取图书详情失败: " + e.getMessage()));
        }
    }

    /**
     * 获取推荐图书
     */
    @GetMapping("/featured")
    public ResponseEntity<?> getFeaturedBooks() {
        try {
            List<Book> books = bookService.getFeaturedBooks();
            return ResponseEntity.ok(books);
        } catch (Exception e) {
            logger.error("获取推荐图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取推荐图书失败: " + e.getMessage()));
        }
    }

    /**
     * 获取热销图书
     */
    @GetMapping("/hot")
    public ResponseEntity<?> getHotBooks() {
        try {
            List<Book> books = bookService.getHotBooks();
            return ResponseEntity.ok(books);
        } catch (Exception e) {
            logger.error("获取热销图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取热销图书失败: " + e.getMessage()));
        }
    }


    /**
     * 获取最新图书
     */
    @GetMapping("/latest")
    public ResponseEntity<?> getLatestBooks() {
        try {
            List<Book> books = bookService.getLatestBooks();
            return ResponseEntity.ok(books);
        } catch (Exception e) {
            logger.error("获取最新图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取最新图书失败: " + e.getMessage()));
        }
    }



    /**
     * 获取高评分图书
     */
    @GetMapping("/high-rated")
    public ResponseEntity<?> getHighRatedBooks() {
        try {
            List<Book> books = bookService.getHighRatedBooks();
            return ResponseEntity.ok(books);
        } catch (Exception e) {
            logger.error("获取高评分图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取高评分图书失败: " + e.getMessage()));
        }
    }

    /**
     * 获取所有分类
     */
    @GetMapping("/categories")
    public ResponseEntity<?> getCategories() {
        try {
            List<String> categories = bookService.getAllCategories();
            return ResponseEntity.ok(categories);
        } catch (Exception e) {
            logger.error("获取分类失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "获取分类失败: " + e.getMessage()));
        }
    }

    /**
     * 创建图书（管理员功能）
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createBook(@RequestBody Book book) {
        try {
            Book createdBook = bookService.createBook(book);
            return ResponseEntity.ok(createdBook);
        } catch (Exception e) {
            logger.error("创建图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "创建图书失败: " + e.getMessage()));
        }
    }

    /**
     * 更新图书（管理员功能）
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateBook(@PathVariable Long id, @RequestBody Book book) {
        try {
            Book updatedBook = bookService.updateBook(id, book);
            return ResponseEntity.ok(updatedBook);
        } catch (Exception e) {
            logger.error("更新图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "更新图书失败: " + e.getMessage()));
        }
    }

    /**
     * 删除图书（管理员功能）
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteBook(@PathVariable Long id) {
        try {
            bookService.deleteBook(id);
            return ResponseEntity.ok(Map.of("message", "删除图书成功"));
        } catch (Exception e) {
            logger.error("删除图书失败: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(Map.of("message", "删除图书失败: " + e.getMessage()));
        }
    }
}
