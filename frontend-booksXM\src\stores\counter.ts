import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authAPI } from '@/api'

export interface User {
  id: number
  username: string
  email: string
  role: 'USER' | 'ADMIN'
  avatar?: string
  createdAt: string
}

export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))

  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'ADMIN')

  // 登录
  const login = async (credentials: { username: string; password: string }) => {
    try {
      const response = await authAPI.login(credentials)
      token.value = response.data.token
      user.value = response.data.user
      localStorage.setItem('token', response.data.token)
      localStorage.setItem('user', JSON.stringify(response.data.user))
      return response
    } catch (error) {
      throw error
    }
  }

  // 注册
  const register = async (userData: { username: string; email: string; password: string }) => {
    try {
      const response = await authAPI.register(userData)
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authAPI.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      token.value = null
      localStorage.removeItem('token')
      localStorage.removeItem('user')
    }
  }

  // 刷新令牌
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken()
      token.value = response.data.token
      localStorage.setItem('token', response.data.token)
      return response
    } catch (error) {
      logout()
      throw error
    }
  }

  // 初始化用户信息
  const initUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('Failed to parse saved user:', error)
        logout()
      }
    }
  }

  return {
    user,
    token,
    isLoggedIn,
    isAdmin,
    login,
    register,
    logout,
    refreshToken,
    initUser
  }
})
