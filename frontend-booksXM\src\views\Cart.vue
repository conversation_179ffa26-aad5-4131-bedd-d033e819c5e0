<template>
  <div class="cart-page">
    <div class="container">
      <div class="page-header">
        <h1 class="page-title">购物车</h1>
        <div class="cart-actions" v-if="cartItems.length > 0">
          <el-button @click="handleSelectAll(!allSelected)" size="small">
            {{ allSelected ? '取消全选' : '全选' }}
          </el-button>
          <el-button @click="handleClearCart" type="danger" size="small">
            清空购物车
          </el-button>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="cartItems.length === 0" class="empty-cart">
        <el-empty description="购物车是空的">
          <el-button type="primary" @click="$router.push('/books')">
            去购物
          </el-button>
        </el-empty>
      </div>

      <div v-else class="cart-content">
        <div class="cart-items">
          <div class="cart-header">
            <el-checkbox v-model="allSelected" @change="handleSelectAll" :indeterminate="indeterminate">
              全选
            </el-checkbox>
            <span class="header-title">商品信息</span>
            <span class="header-price">单价</span>
            <span class="header-quantity">数量</span>
            <span class="header-subtotal">小计</span>
            <span class="header-actions">操作</span>
          </div>

          <div class="cart-item" v-for="item in cartItems" :key="item.id">
            <div class="item-select">
              <el-checkbox v-model="item.selected" @change="handleItemSelect(item)" />
            </div>

            <div class="item-info">
              <div class="item-cover">
                <img :src="item.book.coverUrl || defaultCover" :alt="item.book.title" />
              </div>
              <div class="item-details">
                <h3 class="item-title">{{ item.book.title }}</h3>
                <p class="item-author">{{ item.book.author }}</p>
                <p class="item-category">{{ item.book.category }}</p>
                <div class="item-stock" v-if="item.book.stock < item.quantity">
                  <el-tag type="warning" size="small">库存不足</el-tag>
                </div>
              </div>
            </div>

            <div class="item-price">
              <span class="current-price">¥{{ item.book.price }}</span>
              <span v-if="item.book.originalPrice && item.book.price < item.book.originalPrice" class="original-price">
                ¥{{ item.book.originalPrice }}
              </span>
            </div>

            <div class="item-quantity">
              <el-input-number v-model="item.quantity" :min="1" :max="item.book.stock" size="small"
                @change="handleQuantityChange(item)" :disabled="item.book.stock === 0" />
            </div>

            <div class="item-subtotal">
              <span class="subtotal-price">
                ¥{{ (item.book.price * item.quantity).toFixed(2) }}
              </span>
            </div>

            <div class="item-actions">
              <el-button link size="small" @click="handleRemoveItem(item)" class="remove-btn">
                删除
              </el-button>
            </div>
          </div>
        </div>

        <div class="cart-summary">
          <div class="summary-content">
            <div class="summary-info">
              <span>已选择 {{ selectedItems.length }} 件商品</span>
              <span class="total-quantity">共 {{ totalQuantity }} 本</span>
            </div>

            <div class="summary-price">
              <span class="price-label">合计：</span>
              <span class="total-price">¥{{ totalPrice.toFixed(2) }}</span>
            </div>

            <div class="summary-actions">
              <el-button type="primary" size="large" :disabled="selectedItems.length === 0" @click="handleCheckout"
                class="checkout-btn">
                结算 ({{ selectedItems.length }})
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { useCartStore } from '@/stores/cart'
  import { ElMessage, ElMessageBox } from 'element-plus'

  const router = useRouter()
  const cartStore = useCartStore()

  const loading = ref(false)
  const defaultCover = ref('https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=300&h=400&fit=crop')

  // 计算属性
  const cartItems = computed(() => cartStore.items)
  const selectedItems = computed(() => cartItems.value.filter(item => item.selected))
  const totalQuantity = computed(() => selectedItems.value.reduce((sum, item) => sum + item.quantity, 0))
  const totalPrice = computed(() => selectedItems.value.reduce((sum, item) => sum + (item.book.price * item.quantity), 0))

  const allSelected = computed({
    get: () => cartItems.value.length > 0 && cartItems.value.every(item => item.selected),
    set: (value) => handleSelectAll(value)
  })

  const indeterminate = computed(() => {
    const selectedCount = selectedItems.value.length
    return selectedCount > 0 && selectedCount < cartItems.value.length
  })

  // 获取购物车数据
  const fetchCartData = async () => {
    try {
      loading.value = true
      await cartStore.fetchCart()
    } catch (error) {
      ElMessage.error('获取购物车数据失败')
    } finally {
      loading.value = false
    }
  }

  // 全选/取消全选
  const handleSelectAll = (selected: boolean) => {
    cartStore.toggleAllSelection(selected)
  }

  // 单项选择
  const handleItemSelect = (item: any) => {
    cartStore.toggleItemSelection(item.id)
  }

  // 数量变更
  const handleQuantityChange = async (item: any) => {
    try {
      await cartStore.updateQuantity(item.id, item.quantity)
    } catch (error) {
      ElMessage.error('更新数量失败')
      // 恢复原数量
      await fetchCartData()
    }
  }

  // 删除商品
  const handleRemoveItem = async (item: any) => {
    try {
      await ElMessageBox.confirm(
        `确定要删除 "${item.book.title}" 吗？`,
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await cartStore.removeItem(item.id)
      ElMessage.success('删除成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 清空购物车
  const handleClearCart = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要清空购物车吗？',
        '确认清空',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await cartStore.clearCart()
      ElMessage.success('购物车已清空')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('清空失败')
      }
    }
  }

  // 结算
  const handleCheckout = () => {
    if (selectedItems.value.length === 0) {
      ElMessage.warning('请选择要结算的商品')
      return
    }

    // 检查库存
    const outOfStockItems = selectedItems.value.filter(item => item.book.stock < item.quantity)
    if (outOfStockItems.length > 0) {
      ElMessage.error('部分商品库存不足，请调整数量')
      return
    }

    // 跳转到结算页面
    router.push('/checkout')
  }

  // 页面初始化
  onMounted(() => {
    cartStore.initCart()
    fetchCartData()
    // 滚动到顶部
    window.scrollTo({ top: 0 })
  })
</script>

<style scoped>
  .cart-page {
    min-height: 100vh;
    padding: 20px 0;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }

  .cart-actions {
    display: flex;
    gap: 12px;
  }

  .loading-container {
    background: white;
    border-radius: 8px;
    padding: 24px;
  }

  .empty-cart {
    background: white;
    border-radius: 8px;
    padding: 60px 20px;
    text-align: center;
  }

  .cart-content {
    display: flex;
    gap: 24px;
  }

  .cart-items {
    flex: 1;
    background: white;
    border-radius: 8px;
    overflow: hidden;
  }

  .cart-header {
    display: grid;
    grid-template-columns: 50px 1fr 120px 150px 120px 80px;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 500;
    color: #606266;
  }

  .cart-item {
    display: grid;
    grid-template-columns: 50px 1fr 120px 150px 120px 80px;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s;
  }

  .cart-item:hover {
    background-color: #e6e4e482;
  }

  .cart-item:last-child {
    border-bottom: none;
  }

  .item-info {
    display: flex;
    gap: 16px;
    align-items: center;
  }

  .item-cover {
    width: 80px;
    height: 100px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
  }

  .item-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .item-details {
    flex: 1;
    min-width: 0;
  }

  .item-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .item-author {
    font-size: 14px;
    color: #909399;
    margin: 0 0 4px 0;
  }

  .item-category {
    font-size: 12px;
    color: #c0c4cc;
    margin: 0;
  }

  .item-stock {
    margin-top: 8px;
  }

  .item-price {
    text-align: center;
  }

  .current-price {
    font-size: 16px;
    font-weight: 500;
    color: #e6a23c;
    display: block;
  }

  .original-price {
    font-size: 12px;
    color: #909399;
    text-decoration: line-through;
    display: block;
    margin-top: 4px;
  }

  .item-quantity {
    text-align: center;
  }

  .item-subtotal {
    text-align: center;
  }

  .subtotal-price {
    font-size: 16px;
    font-weight: 500;
    color: #FF2832;
  }

  .item-actions {
    text-align: center;
  }

  .remove-btn {
    color: #f45959;
  }

  .remove-btn:hover {
    color: #fc0000;
    transform: scale(1.5);
  }

  .cart-summary {
    width: 300px;
    background: white;
    border-radius: 8px;
    height: fit-content;
    position: sticky;
    top: 20px;
  }

  .summary-content {
    padding: 24px;
  }

  .summary-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    color: #606266;
    font-size: 14px;
  }

  .total-quantity {
    color: #909399;
  }

  .summary-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
  }

  .price-label {
    font-size: 16px;
    color: #303133;
  }

  .total-price {
    font-size: 24px;
    font-weight: 600;
    color: #FF2832;
  }

  .checkout-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
    background-color: #40a0ff;
  }
  .checkout-btn:hover{
    background-color: #0b85ff;
  }
  @media (max-width: 768px) {
    .cart-content {
      flex-direction: column;
    }

    .cart-summary {
      width: 100%;
      position: static;
    }

    .cart-header {
      display: none;
    }

    .cart-item {
      display: block;
      padding: 16px;
    }

    .item-info {
      margin-bottom: 16px;
    }

    .item-price,
    .item-quantity,
    .item-subtotal,
    .item-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      text-align: left;
    }

    .item-price::before {
      content: '单价：';
    }

    .item-quantity::before {
      content: '数量：';
    }

    .item-subtotal::before {
      content: '小计：';
    }
  }
</style>
