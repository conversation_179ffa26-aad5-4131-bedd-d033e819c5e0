import axios from 'axios'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:9090/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    if (userStore.token) {
      config.headers.Authorization = `Bearer ${userStore.token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    const userStore = useUserStore()

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          userStore.logout()
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败')
    } else {
      ElMessage.error('请求配置错误')
    }

    return Promise.reject(error)
  }
)

export default api

// API接口定义
export const authAPI = {
  login: (data: { username: string; password: string }) =>
    api.post('/auth/login', data),
  register: (data: { username: string; email: string; password: string }) =>
    api.post('/auth/register', data),
  refreshToken: () =>
    api.post('/auth/refresh'),
  logout: () =>
    api.post('/auth/logout')
}

export const bookAPI = {
  getBooks: (params?: {
    page?: number
    size?: number
    sortBy?: string
    sortDir?: string
    category?: string
    search?: string
    title?: string
    author?: string
    minPrice?: number
    maxPrice?: number
    inStock?: boolean
    featured?: boolean
  }) => api.get('/books', { params }),

  getBook: (id: number) =>
    api.get(`/books/${id}`),

  getFeaturedBooks: () =>
    api.get('/books/featured'),

  getHotBooks: () =>
    api.get('/books/hot'),

  getLatestBooks: () =>
    api.get('/books/latest'),

  getHighRatedBooks: () =>
    api.get('/books/high-rated'),

  getCategories: () =>
    api.get('/books/categories'),

  createBook: (data: any) =>
    api.post('/books', data),

  updateBook: (id: number, data: any) =>
    api.put(`/books/${id}`, data),

  deleteBook: (id: number) =>
    api.delete(`/books/${id}`),

  uploadCover: (id: number, file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post(`/books/${id}/cover`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },

  uploadImage: (file: File) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/uploads/image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  }
}

export const cartAPI = {
  getCart: () =>
    api.get('/cart'),
  addToCart: (data: { bookId: number; quantity: number }) =>
    api.post('/cart/add', data),
  updateCartItem: (id: number, quantity: number) =>
    api.put(`/cart/${id}`, { quantity }),
  removeFromCart: (id: number) =>
    api.delete(`/cart/${id}`),
  clearCart: () =>
    api.delete('/cart')
}

export const orderAPI = {
  getOrders: (params?: any) =>
    api.get('/orders/my', { params }),
  getOrder: (id: number) =>
    api.get(`/orders/${id}`),
  createOrder: (data: any) =>
    api.post('/orders', data),
  updateOrderStatus: (id: number, status: string) =>
    api.put(`/orders/${id}/status`, { status }),
  simulatePayment: (orderId: number, paymentMethod: string) =>
    api.post(`/orders/${orderId}/pay`, { paymentMethod }),
  cancelOrder: (id: number) =>
    api.put(`/orders/${id}/cancel`),
  confirmOrder: (id: number) =>
    api.put(`/orders/${id}/confirm`),
  // 管理员获取所有订单
  getAllOrders: (params?: any) =>
    api.get('/orders', { params }),
  // 获取订单统计
  getOrderStats: () =>
    api.get('/orders/stats'),
  // 删除订单（管理员）
  deleteOrder: (id: number) =>
    api.delete(`/orders/${id}`),
  // 发货（管理员）
  shipOrder: (id: number, trackingNumber?: string) =>
    api.put(`/orders/${id}/ship`, { trackingNumber })
}

export const userAPI = {
  getProfile: () =>
    api.get('/user/profile'),
  updateProfile: (data: any) =>
    api.put('/user/profile', data),
  changePassword: (data: { oldPassword: string; newPassword: string }) =>
    api.put('/user/password', data),
  getUserInfo: () =>
    api.get('/user/info'),
  updateUserInfo: (data: any) =>
    api.put('/user/info', data),
  getAddresses: () =>
    api.get('/user/addresses'),
  addAddress: (data: any) =>
    api.post('/user/addresses', data),
  updateAddress: (id: number, data: any) =>
    api.put(`/user/addresses/${id}`, data),
  deleteAddress: (id: number) =>
    api.delete(`/user/addresses/${id}`)
}

export const adminAPI = {
  getDashboard: () =>
    api.get('/admin/dashboard'),
  getUsers: (params?: any) =>
    api.get('/admin/users', { params }),
  updateUserStatus: (id: number, data: { isActive: boolean }) =>
    api.put(`/admin/users/${id}/status`, data),
  updateUserRole: (id: number, data: { role: string }) =>
    api.put(`/admin/users/${id}/role`, data),
  resetUserPassword: (id: number) =>
    api.put(`/admin/users/${id}/reset-password`),
  getOrderStats: (params?: any) =>
    api.get('/admin/orders/stats', { params })
}
