<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-form-wrapper">
        <div class="register-header">
          <h1>注册</h1>
          <p>加入 BooksXM，开启阅读之旅</p>
        </div>

        <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="register-form"
          @submit.prevent="handleRegister">
          <el-form-item prop="username">
            <el-input v-model="registerForm.username" placeholder="用户名" size="large" prefix-icon="User" clearable />
          </el-form-item>

          <el-form-item prop="email">
            <el-input v-model="registerForm.email" type="email" placeholder="邮箱" size="large" prefix-icon="Message"
              clearable />
          </el-form-item>

          <el-form-item prop="password">
            <el-input v-model="registerForm.password" type="password" placeholder="密码" size="large" prefix-icon="Lock"
              show-password />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input v-model="registerForm.confirmPassword" type="password" placeholder="确认密码" size="large"
              prefix-icon="Lock" show-password @keyup.enter="handleRegister" />
          </el-form-item>

          <el-form-item prop="agreement">
            <el-checkbox v-model="registerForm.agreement">
              我已阅读并同意
              <el-button link size="small">《用户协议》</el-button>
              和
              <el-button link size="small">《隐私政策》</el-button>
            </el-checkbox>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="large" :loading="loading" @click="handleRegister" class="register-button">
              注册
            </el-button>
          </el-form-item>
        </el-form>

        <div class="register-footer">
          <span>已有账号？</span>
          <el-button link @click="$router.push('/login')">
            立即登录
          </el-button>
        </div>
      </div>

      <div class="register-banner">
        <div class="banner-content">
          <h2>开启知识之门</h2>
          <p>注册成为会员，享受更多专属服务</p>
          <div class="banner-benefits">
            <div class="benefit-item">
              <el-icon size="20">
                <Present />
              </el-icon>
              <span>新用户专享优惠</span>
            </div>
            <div class="benefit-item">
              <el-icon size="20">
                <Bell />
              </el-icon>
              <span>新书上架提醒</span>
            </div>
            <div class="benefit-item">
              <el-icon size="20">
                <Star />
              </el-icon>
              <span>个性化推荐</span>
            </div>
            <div class="benefit-item">
              <el-icon size="20">
                <Trophy />
              </el-icon>
              <span>会员积分奖励</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive,onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { useUserStore } from '@/stores/user'
  import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
  import { Present, Bell, Star, Trophy } from '@element-plus/icons-vue'

  const router = useRouter()
  const userStore = useUserStore()

  const registerFormRef = ref<FormInstance>()
  const loading = ref(false)

  // 表单数据
  const registerForm = reactive({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreement: false
  })

  // 自定义验证函数
  const validateConfirmPassword = (rule: any, value: any, callback: any) => {
    if (value === '') {
      callback(new Error('请再次输入密码'))
    } else if (value !== registerForm.password) {
      callback(new Error('两次输入密码不一致'))
    } else {
      callback()
    }
  }

  const validateAgreement = (rule: any, value: any, callback: any) => {
    if (!value) {
      callback(new Error('请阅读并同意用户协议'))
    } else {
      callback()
    }
  }

  // 表单验证规则
  const registerRules: FormRules = {
    username: [
      { required: true, message: '请输入用户名', trigger: 'blur' },
      { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' },
      { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
    ],
    email: [
      { required: true, message: '请输入邮箱地址', trigger: 'blur' },
      { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
    ],
    password: [
      { required: true, message: '请输入密码', trigger: 'blur' },
      { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
      { pattern: /^(?=.*[a-zA-Z])(?=.*\d)/, message: '密码必须包含字母和数字', trigger: 'blur' }
    ],
    confirmPassword: [
      { required: true, validator: validateConfirmPassword, trigger: 'blur' }
    ],
    agreement: [
      { required: true, validator: validateAgreement, trigger: 'change' }
    ]
  }

  // 处理注册
  const handleRegister = async () => {
    if (!registerFormRef.value) return

    try {
      await registerFormRef.value.validate()
      loading.value = true

      await userStore.register({
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password
      })

      ElMessage.success('注册成功，请登录')
      router.push('/login')

    } catch (error: any) {
      ElMessage.error(error.message || '注册失败')
    } finally {
      loading.value = false
    }
  }
    // 页面初始化
  onMounted(() => {
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  })
</script>

<style scoped>
  .register-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .register-container {
    display: flex;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
    min-height: 700px;
  }

  .register-form-wrapper {
    flex: 1;
    padding: 60px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .register-header {
    text-align: center;
    margin-bottom: 40px;
  }

  .register-header h1 {
    font-size: 32px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }

  .register-header p {
    color: #909399;
    font-size: 16px;
  }

  .register-form {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }

  .register-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }

  .register-footer {
    text-align: center;
    margin-top: 24px;
    color: #909399;
  }

  .register-banner {
    flex: 1;
    background: linear-gradient(135deg, #36CFC9 0%, #409EFF 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
  }

  .register-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #36CFC9;
    opacity: 0.2;
  }

  .banner-content {
    text-align: center;
    z-index: 1;
    position: relative;
    padding: 40px;
  }

  .banner-content h2 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .banner-content p {
    font-size: 18px;
    opacity: 0.9;
    margin-bottom: 40px;
  }

  .banner-benefits {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    max-width: 300px;
    margin: 0 auto;
  }

  .benefit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    opacity: 0.9;
  }

  @media (max-width: 768px) {
    .register-container {
      flex-direction: column;
      max-width: 400px;
    }

    .register-form-wrapper {
      padding: 40px 20px;
    }

    .register-banner {
      min-height: 250px;
    }

    .banner-content h2 {
      font-size: 24px;
    }

    .banner-content p {
      font-size: 16px;
    }

    .banner-benefits {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
</style>
