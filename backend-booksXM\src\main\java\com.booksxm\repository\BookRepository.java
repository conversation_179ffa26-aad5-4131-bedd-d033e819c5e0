package com.booksxm.repository;

import com.booksxm.entity.Book;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookRepository extends JpaRepository<Book, Long> {

       /**
        * 根据ISBN查找图书
        */
       Optional<Book> findByIsbn(String isbn);

       /**
        * 根据标题查找图书（模糊匹配）
        */
       List<Book> findByTitleContainingIgnoreCase(String title);

       /**
        * 根据作者查找图书（模糊匹配）
        */
       List<Book> findByAuthorContainingIgnoreCase(String author);

       /**
        * 根据分类查找图书
        */
       Page<Book> findByCategory(String category, Pageable pageable);

       /**
        * 根据分类和激活状态查找图书
        */
       Page<Book> findByCategoryAndIsActive(String category, Boolean isActive, Pageable pageable);

       /**
        * 查找激活状态的图书
        */
       Page<Book> findByIsActive(Boolean isActive, Pageable pageable);

       /**
        * 查找推荐图书
        */
       List<Book> findByIsFeaturedTrueAndIsActiveTrueOrderByCreatedAtDesc();

       /**
        * 查找有库存的图书
        */
       Page<Book> findByStockGreaterThanAndIsActive(Integer stock, Boolean isActive, Pageable pageable);

       /**
        * 根据价格范围查找图书
        */
       Page<Book> findByPriceBetweenAndIsActive(BigDecimal minPrice, BigDecimal maxPrice, Boolean isActive,
                     Pageable pageable);

       /**
        * 综合搜索图书（标题、作者、描述）
        */
       @Query("SELECT b FROM Book b WHERE b.isActive = true AND " +
                     "(b.title LIKE %:keyword% OR b.author LIKE %:keyword% OR b.description LIKE %:keyword%)")
       Page<Book> searchBooks(@Param("keyword") String keyword, Pageable pageable);

       /**
        * 高级搜索图书，支持多条件筛选
        */
       @Query("SELECT b FROM Book b WHERE " +
                     "(:title IS NULL OR b.title LIKE %:title%) AND " +
                     "(:author IS NULL OR b.author LIKE %:author%) AND " +
                     "(:category IS NULL OR b.category = :category) AND " +
                     "(:minPrice IS NULL OR b.price >= :minPrice) AND " +
                     "(:maxPrice IS NULL OR b.price <= :maxPrice) AND " +
                     "(:inStock IS NULL OR (:inStock = true AND b.stock > 0) OR (:inStock = false)) AND " +
                     "b.isActive = true")
       Page<Book> findBooksWithFilters(@Param("title") String title,
                     @Param("author") String author,
                     @Param("category") String category,
                     @Param("minPrice") BigDecimal minPrice,
                     @Param("maxPrice") BigDecimal maxPrice,
                     @Param("inStock") Boolean inStock,
                     Pageable pageable);

       /**
        * 查找热销图书（按销量排序）
        */
       List<Book> findTop10ByIsActiveTrueOrderBySalesCountDesc();

       /**
        * 查找最新图书
        */
       List<Book> findTop10ByIsActiveTrueOrderByCreatedAtDesc();

       /**
        * 查找高评分图书
        */
       List<Book> findTop10ByIsActiveTrueAndRatingGreaterThanOrderByRatingDesc(Double minRating);

       /**
        * 获取所有分类
        */
       @Query("SELECT DISTINCT b.category FROM Book b WHERE b.isActive = true ORDER BY b.category")
       List<String> findAllCategories();

       /**
        * 检查ISBN是否已存在
        */
       boolean existsByIsbn(String isbn);

       /**
        * 统计指定分类的图书数量
        */
       Long countByCategoryAndIsActive(String category, Boolean isActive);

       /**
        * 统计库存不足的图书数量
        */
       @Query("SELECT COUNT(b) FROM Book b WHERE b.stock <= :threshold AND b.isActive = true")
       Long countLowStockBooks(@Param("threshold") Integer threshold);

       /**
        * 统计指定时间段内创建的图书数量
        */
       @Query("SELECT COUNT(b) FROM Book b WHERE b.createdAt BETWEEN :startDate AND :endDate")
       Long countBooksByCreatedAtBetween(@Param("startDate") LocalDateTime startDate,
                     @Param("endDate") LocalDateTime endDate);

       /**
        * 获取销量统计
        */
       @Query("SELECT SUM(b.salesCount) FROM Book b WHERE b.isActive = true")
       Long getTotalSalesCount();

       /**
        * 根据销量范围查找图书
        */
       Page<Book> findBySalesCountBetweenAndIsActive(Integer minSales, Integer maxSales, Boolean isActive,
                     Pageable pageable);
}
